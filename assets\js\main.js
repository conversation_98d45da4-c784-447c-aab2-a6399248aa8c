// Main JavaScript file for portfolio website

document.addEventListener("DOMContentLoaded", function () {
  // Mobile menu toggle
  const mobileMenuButton = document.querySelector(".mobile-menu-button");
  const mobileMenu = document.querySelector(".mobile-menu");

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener("click", function () {
      mobileMenu.classList.toggle("hidden");

      // Toggle icon
      const icon = mobileMenuButton.querySelector("i");
      if (mobileMenu.classList.contains("hidden")) {
        icon.className = "fas fa-bars text-xl";
      } else {
        icon.className = "fas fa-times text-xl";
      }
    });
  }

  // Smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });

  // Form validation
  const contactForm = document.querySelector('form[method="POST"]');
  if (contactForm) {
    contactForm.addEventListener("submit", function (e) {
      const name = this.querySelector('input[name="name"]');
      const email = this.querySelector('input[name="email"]');
      const subject = this.querySelector('input[name="subject"]');
      const message = this.querySelector('textarea[name="message"]');

      let isValid = true;
      let errorMessage = "";

      // Reset previous error styles
      [name, email, subject, message].forEach((field) => {
        if (field) {
          field.classList.remove("border-red-500");
        }
      });

      // Validate required fields
      if (name && !name.value.trim()) {
        name.classList.add("border-red-500");
        errorMessage += "Nama harus diisi.\n";
        isValid = false;
      }

      if (email && !email.value.trim()) {
        email.classList.add("border-red-500");
        errorMessage += "Email harus diisi.\n";
        isValid = false;
      } else if (email && !isValidEmail(email.value)) {
        email.classList.add("border-red-500");
        errorMessage += "Format email tidak valid.\n";
        isValid = false;
      }

      if (subject && !subject.value.trim()) {
        subject.classList.add("border-red-500");
        errorMessage += "Subject harus diisi.\n";
        isValid = false;
      }

      if (message && !message.value.trim()) {
        message.classList.add("border-red-500");
        errorMessage += "Pesan harus diisi.\n";
        isValid = false;
      }

      if (!isValid) {
        e.preventDefault();
        alert(errorMessage);
      }
    });
  }

  // Newsletter form validation
  const newsletterForm = document.querySelector("form");
  if (newsletterForm && newsletterForm.querySelector('input[type="email"]')) {
    newsletterForm.addEventListener("submit", function (e) {
      e.preventDefault();
      const emailInput = this.querySelector('input[type="email"]');

      if (!emailInput.value.trim()) {
        alert("Email harus diisi!");
        return;
      }

      if (!isValidEmail(emailInput.value)) {
        alert("Format email tidak valid!");
        return;
      }

      // Simulate newsletter subscription
      alert("Terima kasih! Anda berhasil subscribe newsletter.");
      emailInput.value = "";
    });
  }

  // Scroll to top button
  createScrollToTopButton();

  // Lazy loading for images
  if ("IntersectionObserver" in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove("lazy");
          imageObserver.unobserve(img);
        }
      });
    });

    document.querySelectorAll("img[data-src]").forEach((img) => {
      imageObserver.observe(img);
    });
  }

  // Animate elements on scroll
  animateOnScroll();
});

// Email validation function
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Create scroll to top button
function createScrollToTopButton() {
  const scrollButton = document.createElement("button");
  scrollButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
  scrollButton.className =
    "fixed bottom-6 right-6 bg-primary text-white w-12 h-12 rounded-full shadow-lg hover:bg-secondary transition-all duration-300 opacity-0 pointer-events-none z-50";
  scrollButton.id = "scrollToTop";

  document.body.appendChild(scrollButton);

  // Show/hide button based on scroll position
  window.addEventListener("scroll", function () {
    if (window.pageYOffset > 300) {
      scrollButton.classList.remove("opacity-0", "pointer-events-none");
    } else {
      scrollButton.classList.add("opacity-0", "pointer-events-none");
    }
  });

  // Scroll to top when clicked
  scrollButton.addEventListener("click", function () {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  });
}

// Animate elements on scroll
function animateOnScroll() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("animate-fade-in");
      }
    });
  }, observerOptions);

  // Observe elements that should animate
  document.querySelectorAll(".animate-on-scroll").forEach((el) => {
    observer.observe(el);
  });
}

// Copy to clipboard function
function copyToClipboard(text) {
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(text || window.location.href)
      .then(function () {
        showNotification("Link berhasil disalin!", "success");
      })
      .catch(function () {
        fallbackCopyToClipboard(text || window.location.href);
      });
  } else {
    fallbackCopyToClipboard(text || window.location.href);
  }
}

// Fallback copy to clipboard for older browsers
function fallbackCopyToClipboard(text) {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  textArea.style.position = "fixed";
  textArea.style.left = "-999999px";
  textArea.style.top = "-999999px";
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    document.execCommand("copy");
    showNotification("Link berhasil disalin!", "success");
  } catch (err) {
    showNotification("Gagal menyalin link", "error");
  }

  document.body.removeChild(textArea);
}

// Show notification
function showNotification(message, type = "info") {
  const notification = document.createElement("div");
  notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full`;

  if (type === "success") {
    notification.className += " bg-green-500 text-white";
  } else if (type === "error") {
    notification.className += " bg-red-500 text-white";
  } else {
    notification.className += " bg-blue-500 text-white";
  }

  notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${
              type === "success" ? "check" : type === "error" ? "times" : "info"
            }-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;

  document.body.appendChild(notification);

  // Animate in
  setTimeout(() => {
    notification.classList.remove("translate-x-full");
  }, 100);

  // Animate out and remove
  setTimeout(() => {
    notification.classList.add("translate-x-full");
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 300);
  }, 3000);
}

// Add CSS for animations and gradient effects
const style = document.createElement("style");
style.textContent = `
    /* Poppins Font Application */
    * {
        font-family: 'Poppins', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    }

    body {
        font-family: 'Poppins', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    }

    .animate-fade-in {
        animation: fadeInUp 0.6s ease-out forwards;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .lazy {
        opacity: 0;
        transition: opacity 0.3s;
    }

    .lazy.loaded {
        opacity: 1;
    }

    /* Enhanced gradient card effects */
    .gradient-card {
        position: relative;
        overflow: hidden;
    }

    .gradient-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .gradient-card:hover::before {
        left: 100%;
    }

    /* Floating animation for cards */
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .float-animation {
        animation: float 3s ease-in-out infinite;
    }

    /* Pulse effect for icons */
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .pulse-animation {
        animation: pulse 2s ease-in-out infinite;
    }

    /* Gradient text effect */
    .gradient-text {
        background: linear-gradient(45deg, #3B82F6, #8B5CF6, #EC4899);
        background-size: 200% 200%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: gradientShift 3s ease infinite;
    }

    @keyframes gradientShift {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    /* Enhanced shadow effects */
    .shadow-glow {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        transition: box-shadow 0.3s ease;
    }

    .shadow-glow:hover {
        box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
    }
`;
document.head.appendChild(style);
