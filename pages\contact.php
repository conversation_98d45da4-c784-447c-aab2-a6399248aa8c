<?php
// Handle form submission
$message_sent = false;
$error_message = '';

if ($_POST && isset($_POST['send_message'])) {
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $subject = sanitize_input($_POST['subject'] ?? '');
    $message = sanitize_input($_POST['message'] ?? '');
    
    // Basic validation
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error_message = 'Semua field harus diisi.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Format email tidak valid.';
    } else {
        // Here you would typically save to database or send email
        // For now, we'll just show success message
        $message_sent = true;
        
        // You can add email sending logic here
        // mail($to, $subject, $message, $headers);
    }
}
?>

<!-- Contact Header -->
<section class="bg-gradient-to-r from-primary to-secondary text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl lg:text-5xl font-bold mb-4">Contact Me</h1>
            <p class="text-xl text-blue-100 max-w-2xl mx-auto">
                Mari berkolaborasi dan wujudkan ide-ide kreatif bersama
            </p>
        </div>
    </div>
</section>

<!-- Contact Content -->
<section class="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Information -->
            <div class="bg-gradient-to-br from-white to-blue-50 rounded-lg p-8 shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300">
                <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-8">Get In Touch</h2>
                
                <!-- Contact Details -->
                <div class="space-y-6 mb-8">
                    <div class="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 hover:shadow-md transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-envelope text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">Email</h3>
                            <p class="text-gray-600 font-medium"><EMAIL></p>
                        </div>
                    </div>

                    <div class="flex items-center gap-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 hover:shadow-md transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-phone text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">Phone</h3>
                            <p class="text-gray-600 font-medium">+62 813 - 1075 - 6915</p>
                        </div>
                    </div>

                    <div class="flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100 hover:shadow-md transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-map-marker-alt text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">Location</h3>
                            <p class="text-gray-600 font-medium">Tanjungpinang, Indonesia</p>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div class="p-6 bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg border border-gray-200">
                    <h3 class="text-xl font-semibold bg-gradient-to-r from-gray-900 to-slate-600 bg-clip-text text-transparent mb-4">Follow Me</h3>
                    <div class="flex gap-4">
                        <a href="#" class="w-12 h-12 bg-gradient-to-br from-gray-700 to-gray-900 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-all duration-300 hover:scale-110 group">
                            <i class="fab fa-github group-hover:scale-110 transition-transform duration-300"></i>
                        </a>
                        <a href="#" class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-all duration-300 hover:scale-110 group">
                            <i class="fab fa-linkedin-in group-hover:scale-110 transition-transform duration-300"></i>
                        </a>
                        <a href="#" class="w-12 h-12 bg-gradient-to-br from-pink-400 to-pink-600 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-all duration-300 hover:scale-110 group">
                            <i class="fab fa-instagram group-hover:scale-110 transition-transform duration-300"></i>
                        </a>
                        <a href="#" class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-all duration-300 hover:scale-110 group">
                            <i class="fab fa-twitter group-hover:scale-110 transition-transform duration-300"></i>
                        </a>
                        <a href="#" class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-all duration-300 hover:scale-110 group">
                            <i class="fab fa-whatsapp group-hover:scale-110 transition-transform duration-300"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Contact Form -->
            <div class="bg-gradient-to-br from-white to-indigo-50 rounded-lg shadow-xl p-8 border border-indigo-100 hover:shadow-2xl transition-all duration-300 relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary to-indigo-500"></div>
                <h3 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-indigo-600 bg-clip-text text-transparent mb-6">Send Message</h3>
                
                <?php if ($message_sent): ?>
                    <div class="mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-green-700 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <span class="font-medium">Pesan Anda berhasil dikirim! Saya akan segera merespons.</span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="mb-6 p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 text-red-700 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-br from-red-400 to-pink-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-exclamation text-white text-sm"></i>
                            </div>
                            <span class="font-medium"><?php echo $error_message; ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Nama Lengkap *
                            </label>
                            <input type="text"
                                   id="name"
                                   name="name"
                                   required
                                   value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300">
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email Address *
                            </label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   required
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300">
                        </div>
                    </div>
                    
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                            Subject *
                        </label>
                        <input type="text"
                               id="subject"
                               name="subject"
                               required
                               value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300">
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                            Message *
                        </label>
                        <textarea id="message"
                                  name="message"
                                  rows="6"
                                  required
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300"
                                  placeholder="Ceritakan tentang project atau kolaborasi yang Anda inginkan..."><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                    </div>
                    
                    <button type="submit"
                            name="send_message"
                            class="w-full bg-gradient-to-r from-primary to-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-secondary hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 group">
                        <i class="fas fa-paper-plane mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        Send Message
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Services CTA Section -->
<section class="py-16 bg-gradient-to-br from-primary via-indigo-600 to-secondary relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div class="absolute top-32 right-20 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
        <div class="absolute bottom-20 left-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <!-- Icon Badge -->
        <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-white/20 to-white/10 rounded-full shadow-lg mb-6 backdrop-blur-sm border border-white/30">
            <i class="fas fa-briefcase text-white text-3xl"></i>
        </div>

        <h2 class="text-3xl font-bold text-white mb-4">
            <span class="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                Interested in My Services?
            </span>
        </h2>
        <p class="text-blue-100 mb-8 max-w-2xl mx-auto">
            Lihat detail layanan dan harga yang saya tawarkan untuk kebutuhan digital Anda
        </p>

        <!-- Service Categories -->
        <div class="flex flex-wrap justify-center items-center gap-6 mb-8 text-sm">
            <div class="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                <div class="w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"></div>
                <span class="text-blue-200">UI/UX Design</span>
            </div>
            <div class="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
                <span class="text-blue-200">Web Development</span>
            </div>
            <div class="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                <div class="w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full"></div>
                <span class="text-blue-200">Presentation Design</span>
            </div>
        </div>

        <!-- CTA Button -->
        <a href="index.php?page=services"
           class="inline-flex items-center bg-gradient-to-r from-white to-blue-50 text-primary px-8 py-4 rounded-xl font-semibold hover:from-blue-50 hover:to-white transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 group">
            <i class="fas fa-eye mr-3 group-hover:scale-110 transition-transform duration-300"></i>
            <span class="mr-2">View Services & Pricing</span>
            <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
        </a>

        <!-- Additional Info -->
        <div class="mt-8 text-blue-200 text-sm">
            <p>Mulai dari Rp 150.000 • Konsultasi Gratis • Harga Terjangkau</p>
        </div>
    </div>
</section>


