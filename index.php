<?php
session_start();

// Simple routing
$page = $_GET['page'] ?? 'home';

// Include configuration
include_once 'config/database.php';
include_once 'includes/functions.php';

// Valid pages
$valid_pages = ['home', 'articles', 'contact', 'admin'];
if (!in_array($page, $valid_pages)) {
    $page = 'home';
}

// Page title mapping
$page_titles = [
    'home' => 'Portfolio - Home',
    'articles' => 'Portfolio - Articles', 
    'contact' => 'Portfolio - Contact Me',
    'admin' => 'Portfolio - Admin'
];

$page_title = $page_titles[$page];
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Poppins', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
                    },
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen font-sans">
    <!-- Navigation -->
    <?php include 'includes/navigation.php'; ?>
    
    <!-- Main Content -->
    <main>
        <?php
        switch($page) {
            case 'home':
                include 'pages/home.php';
                break;
            case 'articles':
                include 'pages/articles.php';
                break;
            case 'contact':
                include 'pages/contact.php';
                break;
            case 'admin':
                include 'pages/admin.php';
                break;
            default:
                include 'pages/home.php';
        }
        ?>
    </main>
    
    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <script src="assets/js/main.js"></script>
</body>
</html>
