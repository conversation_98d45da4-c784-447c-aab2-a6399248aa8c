<?php
// Helper functions

function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirect($url) {
    header("Location: $url");
    exit();
}

function is_admin() {
    return isset($_SESSION['admin']) && $_SESSION['admin'] === true;
}

function require_admin() {
    if (!is_admin()) {
        redirect('index.php?page=admin');
    }
}

function get_articles($limit = null) {
    global $pdo;
    
    $sql = "SELECT * FROM articles WHERE status = 'published' ORDER BY created_at DESC";
    if ($limit) {
        $sql .= " LIMIT $limit";
    }
    
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return [];
    }
}

function get_article_by_id($id) {
    global $pdo;
    
    $sql = "SELECT * FROM articles WHERE id = :id";
    
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return false;
    }
}

function format_date($date) {
    return date('d F Y', strtotime($date));
}

function truncate_text($text, $length = 150) {
    if (strlen($text) > $length) {
        return substr($text, 0, $length) . '...';
    }
    return $text;
}
?>
