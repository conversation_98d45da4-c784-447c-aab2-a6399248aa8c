<?php
// Handle logout first (before any output)
if (isset($_GET['logout'])) {
    session_destroy();
    redirect('index.php?page=admin');
}

// Check if user is logged in
$is_logged_in = is_admin();

// Handle login
if ($_POST && isset($_POST['login'])) {
    $username = sanitize_input($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    // Simple authentication (in production, use proper password hashing)
    if ($username === 'admin' && $password === 'admin123') {
        $_SESSION['admin'] = true;
        $is_logged_in = true;
    } else {
        $login_error = 'Username atau password salah!';
    }
}

// Handle article operations
if ($is_logged_in && $_POST) {
    if (isset($_POST['add_article'])) {
        $title = sanitize_input($_POST['title']);
        $content = $_POST['content']; // Don't sanitize content as it may contain HTML
        $category = sanitize_input($_POST['category']);
        $tags = sanitize_input($_POST['tags']);
        $status = sanitize_input($_POST['status']);
        
        try {
            $stmt = $pdo->prepare("INSERT INTO articles (title, content, category, tags, status, created_at) VALUES (:title, :content, :category, :tags, :status, NOW())");
            $stmt->bindParam(':title', $title);
            $stmt->bindParam(':content', $content);
            $stmt->bindParam(':category', $category);
            $stmt->bindParam(':tags', $tags);
            $stmt->bindParam(':status', $status);
            $stmt->execute();
            $success_message = 'Artikel berhasil ditambahkan!';
        } catch(PDOException $e) {
            $error_message = 'Error: ' . $e->getMessage();
        }
    }
    
    if (isset($_POST['delete_article'])) {
        $article_id = $_POST['article_id'];
        try {
            $stmt = $pdo->prepare("DELETE FROM articles WHERE id = :id");
            $stmt->bindParam(':id', $article_id);
            $stmt->execute();
            $success_message = 'Artikel berhasil dihapus!';
        } catch(PDOException $e) {
            $error_message = 'Error: ' . $e->getMessage();
        }
    }
}

// Get all articles for admin
if ($is_logged_in) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM articles ORDER BY created_at DESC");
        $stmt->execute();
        $all_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        $all_articles = [];
    }
}
?>

<?php if (!$is_logged_in): ?>
    <!-- Login Form -->
    <section class="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="bg-gradient-to-br from-white to-blue-50 rounded-lg shadow-xl p-8 border border-blue-100">
                <div class="text-center">
                    <div class="mx-auto h-16 w-16 bg-gradient-to-br from-primary to-indigo-600 rounded-full flex items-center justify-center shadow-lg mb-6">
                        <i class="fas fa-lock text-white text-2xl"></i>
                    </div>
                    <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-2">
                        Admin Login
                    </h2>
                    <p class="text-gray-600 mb-6">
                        Masuk untuk mengelola konten portfolio
                    </p>
                </div>

                <?php if (isset($login_error)): ?>
                    <div class="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg shadow-md mb-6">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-br from-red-400 to-pink-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-exclamation text-white text-sm"></i>
                            </div>
                            <span class="font-medium"><?php echo $login_error; ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <form class="space-y-6" method="POST">
                    <div class="space-y-4">
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input id="username"
                                   name="username"
                                   type="text"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300"
                                   placeholder="Masukkan username">
                        </div>
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input id="password"
                                   name="password"
                                   type="password"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300"
                                   placeholder="Masukkan password">
                        </div>
                    </div>

                    <button type="submit"
                            name="login"
                            class="w-full bg-gradient-to-r from-primary to-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-secondary hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 group">
                        <i class="fas fa-sign-in-alt mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        Sign In
                    </button>
                </form>

                
            </div>
        </div>
    </section>

<?php else: ?>
    <!-- Admin Dashboard -->
    <section class="py-8 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-gradient-to-br from-white to-blue-50 rounded-lg shadow-xl p-6 mb-8 border border-blue-100 relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary to-indigo-500"></div>
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent">Admin Dashboard</h1>
                        <p class="text-gray-600 mt-1">Kelola konten portfolio Anda</p>
                    </div>
                    <a href="index.php?page=admin&logout=1"
                       class="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 group">
                        <i class="fas fa-sign-out-alt mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>Logout
                    </a>
                </div>
            </div>
            
            <!-- Success/Error Messages -->
            <?php if (isset($success_message)): ?>
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg shadow-md mb-6">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                        <span class="font-medium"><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg shadow-md mb-6">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-red-400 to-pink-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-exclamation text-white text-sm"></i>
                        </div>
                        <span class="font-medium"><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-gradient-to-br from-white to-blue-50 rounded-lg shadow-lg p-6 border border-blue-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                    <div class="flex items-center">
                        <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-newspaper text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Articles</p>
                            <p class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent"><?php echo count($all_articles); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-white to-green-50 rounded-lg shadow-lg p-6 border border-green-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                    <div class="flex items-center">
                        <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-check-circle text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Published</p>
                            <p class="text-2xl font-bold bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
                                <?php echo count(array_filter($all_articles, function($a) { return $a['status'] === 'published'; })); ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-br from-white to-yellow-50 rounded-lg shadow-lg p-6 border border-yellow-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                    <div class="flex items-center">
                        <div class="p-3 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-clock text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Draft</p>
                            <p class="text-2xl font-bold bg-gradient-to-r from-yellow-600 to-yellow-700 bg-clip-text text-transparent">
                                <?php echo count(array_filter($all_articles, function($a) { return $a['status'] === 'draft'; })); ?>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-white to-purple-50 rounded-lg shadow-lg p-6 border border-purple-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                    <div class="flex items-center">
                        <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-eye text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Views</p>
                            <p class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">
                                <?php echo number_format(array_sum(array_column($all_articles, 'views'))); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tabs -->
            <div class="bg-gradient-to-br from-white to-gray-50 rounded-lg shadow-xl border border-gray-100">
                <div class="border-b border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg">
                    <nav class="-mb-px flex">
                        <button onclick="showTab('articles')"
                                id="articles-tab"
                                class="tab-button py-4 px-6 border-b-2 border-primary text-primary font-semibold text-sm bg-gradient-to-r from-blue-50 to-indigo-50 rounded-tl-lg">
                            <i class="fas fa-list mr-2"></i>Manage Articles
                        </button>
                        <button onclick="showTab('add-article')"
                                id="add-article-tab"
                                class="tab-button py-4 px-6 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-semibold text-sm hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-300">
                            <i class="fas fa-plus mr-2"></i>Add New Article
                        </button>
                    </nav>
                </div>
                
                <!-- Articles List Tab -->
                <div id="articles-content" class="tab-content p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">All Articles</h3>
                    
                    <?php if (empty($all_articles)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-newspaper text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500">Belum ada artikel. Tambahkan artikel pertama Anda!</p>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($all_articles as $article): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($article['title']); ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                    <?php echo htmlspecialchars($article['category'] ?? 'General'); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                    <?php echo $article['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                                    <?php echo ucfirst($article['status']); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($article['views'] ?? 0); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php echo format_date($article['created_at']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <a href="article.php?id=<?php echo $article['id']; ?>" 
                                                   class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                                                <form method="POST" class="inline" onsubmit="return confirm('Are you sure?')">
                                                    <input type="hidden" name="article_id" value="<?php echo $article['id']; ?>">
                                                    <button type="submit" name="delete_article" class="text-red-600 hover:text-red-900">
                                                        Delete
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Add Article Tab -->
                <div id="add-article-content" class="tab-content p-6 hidden bg-gradient-to-br from-white to-blue-50">
                    <div class="bg-gradient-to-r from-white to-indigo-50 rounded-lg p-6 border border-indigo-100 shadow-md">
                        <h3 class="text-lg font-semibold bg-gradient-to-r from-gray-900 to-indigo-600 bg-clip-text text-transparent mb-6">
                            <i class="fas fa-plus-circle mr-2"></i>Add New Article
                        </h3>

                        <form method="POST" class="space-y-6">
                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                    Article Title *
                                </label>
                                <input type="text"
                                       id="title"
                                       name="title"
                                       required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300"
                                       placeholder="Enter article title">
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                        Category
                                    </label>
                                    <select id="category"
                                            name="category"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300">
                                        <option value="Technology">Technology</option>
                                        <option value="Programming">Programming</option>
                                        <option value="Web Development">Web Development</option>
                                        <option value="Tutorial">Tutorial</option>
                                        <option value="Tips">Tips</option>
                                        <option value="General">General</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                        Status
                                    </label>
                                    <select id="status"
                                            name="status"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300">
                                        <option value="draft">Draft</option>
                                        <option value="published">Published</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
                                    Tags (separated by commas)
                                </label>
                                <input type="text"
                                       id="tags"
                                       name="tags"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300"
                                       placeholder="php, web development, tutorial">
                            </div>

                            <div>
                                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                                    Article Content *
                                </label>
                                <textarea id="content"
                                          name="content"
                                          rows="15"
                                          required
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-gradient-to-r from-white to-blue-50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300"
                                          placeholder="Write your article content here. You can use HTML tags for formatting."></textarea>
                                <p class="mt-2 text-sm text-gray-500">
                                    You can use HTML tags for formatting (p, h1-h6, strong, em, ul, ol, li, a, img, etc.)
                                </p>
                            </div>

                            <div class="flex justify-end">
                                <button type="submit"
                                        name="add_article"
                                        class="bg-gradient-to-r from-primary to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-secondary hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 group">
                                    <i class="fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300"></i>
                                    Add Article
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.add('hidden'));

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(tabName + '-content').classList.remove('hidden');

            // Add active class to selected tab button
            const activeButton = document.getElementById(tabName + '-tab');
            activeButton.classList.remove('border-transparent', 'text-gray-500');
            activeButton.classList.add('border-primary', 'text-primary');
        }
    </script>
<?php endif; ?>
