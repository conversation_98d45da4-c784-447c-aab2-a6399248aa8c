<?php
// Check if user is logged in
$is_logged_in = is_admin();

// Handle login
if ($_POST && isset($_POST['login'])) {
    $username = sanitize_input($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    // Simple authentication (in production, use proper password hashing)
    if ($username === 'admin' && $password === 'admin123') {
        $_SESSION['admin'] = true;
        $is_logged_in = true;
    } else {
        $login_error = 'Username atau password salah!';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    redirect('index.php?page=admin');
}

// Handle article operations
if ($is_logged_in && $_POST) {
    if (isset($_POST['add_article'])) {
        $title = sanitize_input($_POST['title']);
        $content = $_POST['content']; // Don't sanitize content as it may contain HTML
        $category = sanitize_input($_POST['category']);
        $tags = sanitize_input($_POST['tags']);
        $status = sanitize_input($_POST['status']);
        
        try {
            $stmt = $pdo->prepare("INSERT INTO articles (title, content, category, tags, status, created_at) VALUES (:title, :content, :category, :tags, :status, NOW())");
            $stmt->bindParam(':title', $title);
            $stmt->bindParam(':content', $content);
            $stmt->bindParam(':category', $category);
            $stmt->bindParam(':tags', $tags);
            $stmt->bindParam(':status', $status);
            $stmt->execute();
            $success_message = 'Artikel berhasil ditambahkan!';
        } catch(PDOException $e) {
            $error_message = 'Error: ' . $e->getMessage();
        }
    }
    
    if (isset($_POST['delete_article'])) {
        $article_id = $_POST['article_id'];
        try {
            $stmt = $pdo->prepare("DELETE FROM articles WHERE id = :id");
            $stmt->bindParam(':id', $article_id);
            $stmt->execute();
            $success_message = 'Artikel berhasil dihapus!';
        } catch(PDOException $e) {
            $error_message = 'Error: ' . $e->getMessage();
        }
    }
}

// Get all articles for admin
if ($is_logged_in) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM articles ORDER BY created_at DESC");
        $stmt->execute();
        $all_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        $all_articles = [];
    }
}
?>

<?php if (!$is_logged_in): ?>
    <!-- Login Form -->
    <section class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="mx-auto h-12 w-12 bg-primary rounded-full flex items-center justify-center">
                    <i class="fas fa-lock text-white text-xl"></i>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Admin Login
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Masuk untuk mengelola konten portfolio
                </p>
            </div>
            
            <?php if (isset($login_error)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <?php echo $login_error; ?>
                </div>
            <?php endif; ?>
            
            <form class="mt-8 space-y-6" method="POST">
                <div class="rounded-md shadow-sm -space-y-px">
                    <div>
                        <label for="username" class="sr-only">Username</label>
                        <input id="username" 
                               name="username" 
                               type="text" 
                               required 
                               class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm" 
                               placeholder="Username">
                    </div>
                    <div>
                        <label for="password" class="sr-only">Password</label>
                        <input id="password" 
                               name="password" 
                               type="password" 
                               required 
                               class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm" 
                               placeholder="Password">
                    </div>
                </div>

                <div>
                    <button type="submit" 
                            name="login"
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt text-primary-light group-hover:text-primary-dark"></i>
                        </span>
                        Sign in
                    </button>
                </div>
                
                <div class="text-center text-sm text-gray-600">
                    <p>Demo credentials:</p>
                    <p><strong>Username:</strong> admin</p>
                    <p><strong>Password:</strong> admin123</p>
                </div>
            </form>
        </div>
    </section>

<?php else: ?>
    <!-- Admin Dashboard -->
    <section class="py-8 bg-gray-50 min-h-screen">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                        <p class="text-gray-600">Kelola konten portfolio Anda</p>
                    </div>
                    <a href="index.php?page=admin&logout=1" 
                       class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </a>
                </div>
            </div>
            
            <!-- Success/Error Messages -->
            <?php if (isset($success_message)): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    <i class="fas fa-check-circle mr-2"></i><?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <i class="fas fa-newspaper text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Articles</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo count($all_articles); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i class="fas fa-check-circle text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Published</p>
                            <p class="text-2xl font-semibold text-gray-900">
                                <?php echo count(array_filter($all_articles, function($a) { return $a['status'] === 'published'; })); ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <i class="fas fa-clock text-yellow-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Draft</p>
                            <p class="text-2xl font-semibold text-gray-900">
                                <?php echo count(array_filter($all_articles, function($a) { return $a['status'] === 'draft'; })); ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <i class="fas fa-eye text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Views</p>
                            <p class="text-2xl font-semibold text-gray-900">
                                <?php echo number_format(array_sum(array_column($all_articles, 'views'))); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tabs -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex">
                        <button onclick="showTab('articles')" 
                                id="articles-tab"
                                class="tab-button py-4 px-6 border-b-2 border-primary text-primary font-medium text-sm">
                            Manage Articles
                        </button>
                        <button onclick="showTab('add-article')" 
                                id="add-article-tab"
                                class="tab-button py-4 px-6 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                            Add New Article
                        </button>
                    </nav>
                </div>
                
                <!-- Articles List Tab -->
                <div id="articles-content" class="tab-content p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">All Articles</h3>
                    
                    <?php if (empty($all_articles)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-newspaper text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500">Belum ada artikel. Tambahkan artikel pertama Anda!</p>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($all_articles as $article): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($article['title']); ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                    <?php echo htmlspecialchars($article['category'] ?? 'General'); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                    <?php echo $article['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                                    <?php echo ucfirst($article['status']); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($article['views'] ?? 0); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php echo format_date($article['created_at']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <a href="article.php?id=<?php echo $article['id']; ?>" 
                                                   class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                                                <form method="POST" class="inline" onsubmit="return confirm('Are you sure?')">
                                                    <input type="hidden" name="article_id" value="<?php echo $article['id']; ?>">
                                                    <button type="submit" name="delete_article" class="text-red-600 hover:text-red-900">
                                                        Delete
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Add Article Tab -->
                <div id="add-article-content" class="tab-content p-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Add New Article</h3>

                    <form method="POST" class="space-y-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                Article Title *
                            </label>
                            <input type="text"
                                   id="title"
                                   name="title"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Enter article title">
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                    Category
                                </label>
                                <select id="category"
                                        name="category"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="Technology">Technology</option>
                                    <option value="Programming">Programming</option>
                                    <option value="Web Development">Web Development</option>
                                    <option value="Tutorial">Tutorial</option>
                                    <option value="Tips">Tips</option>
                                    <option value="General">General</option>
                                </select>
                            </div>

                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                    Status
                                </label>
                                <select id="status"
                                        name="status"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="draft">Draft</option>
                                    <option value="published">Published</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
                                Tags (separated by commas)
                            </label>
                            <input type="text"
                                   id="tags"
                                   name="tags"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="php, web development, tutorial">
                        </div>

                        <div>
                            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                                Article Content *
                            </label>
                            <textarea id="content"
                                      name="content"
                                      rows="15"
                                      required
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                      placeholder="Write your article content here. You can use HTML tags for formatting."></textarea>
                            <p class="mt-2 text-sm text-gray-500">
                                You can use HTML tags for formatting (p, h1-h6, strong, em, ul, ol, li, a, img, etc.)
                            </p>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit"
                                    name="add_article"
                                    class="bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-secondary transition-colors">
                                <i class="fas fa-plus mr-2"></i>
                                Add Article
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.add('hidden'));

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(tabName + '-content').classList.remove('hidden');

            // Add active class to selected tab button
            const activeButton = document.getElementById(tabName + '-tab');
            activeButton.classList.remove('border-transparent', 'text-gray-500');
            activeButton.classList.add('border-primary', 'text-primary');
        }
    </script>
<?php endif; ?>
