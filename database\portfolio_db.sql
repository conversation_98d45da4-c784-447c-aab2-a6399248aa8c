-- Portfolio Database Schema
-- Create database and tables for the portfolio website

-- Create database
CREATE DATABASE IF NOT EXISTS portfolio_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE portfolio_db;

-- Articles table
CREATE TABLE IF NOT EXISTS articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category VARCHAR(100) DEFAULT 'General',
    tags TEXT,
    image VARCHAR(255),
    status ENUM('draft', 'published') DEFAULT 'draft',
    views INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHA<PERSON>(200) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('unread', 'read', 'replied') DEFAULT 'unread',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Newsletter subscribers table
CREATE TABLE IF NOT EXISTS newsletter_subscribers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) NOT NULL UNIQUE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Portfolio projects table
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image VARCHAR(255),
    technologies TEXT,
    project_url VARCHAR(255),
    github_url VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Skills table
CREATE TABLE IF NOT EXISTS skills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    level ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'intermediate',
    icon VARCHAR(100),
    order_index INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- Experience table
CREATE TABLE IF NOT EXISTS experience (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company VARCHAR(255) NOT NULL,
    position VARCHAR(255) NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    is_current BOOLEAN DEFAULT FALSE,
    location VARCHAR(100),
    order_index INT DEFAULT 0
);

-- Education table
CREATE TABLE IF NOT EXISTS education (
    id INT AUTO_INCREMENT PRIMARY KEY,
    institution VARCHAR(255) NOT NULL,
    degree VARCHAR(255) NOT NULL,
    field_of_study VARCHAR(255),
    start_date DATE NOT NULL,
    end_date DATE,
    is_current BOOLEAN DEFAULT FALSE,
    gpa VARCHAR(10),
    description TEXT,
    order_index INT DEFAULT 0
);

-- Insert sample data

-- Sample articles
INSERT INTO articles (title, content, excerpt, category, tags, status, views) VALUES
('Tips Optimasi Performance PHP', 
 '<h2>Pendahuluan</h2><p>Performance adalah salah satu aspek terpenting dalam pengembangan aplikasi PHP. Artikel ini akan membahas berbagai teknik optimasi yang dapat meningkatkan performa aplikasi PHP Anda.</p><h2>1. Gunakan OPcache</h2><p>OPcache adalah ekstensi PHP yang meningkatkan performa dengan menyimpan bytecode yang telah dikompilasi dalam memori.</p><h2>2. Optimasi Database Query</h2><p>Pastikan query database Anda efisien dengan menggunakan index yang tepat dan menghindari N+1 query problem.</p><h2>3. Caching Strategy</h2><p>Implementasikan strategi caching yang tepat menggunakan Redis atau Memcached untuk data yang sering diakses.</p>',
 'Pelajari teknik-teknik optimasi untuk meningkatkan performa aplikasi PHP Anda.',
 'Programming',
 'php, performance, optimization, opcache',
 'published',
 1234),

('Database Design Best Practices',
 '<h2>Prinsip Dasar Database Design</h2><p>Design database yang baik adalah fondasi dari aplikasi yang scalable dan maintainable.</p><h2>Normalisasi</h2><p>Normalisasi membantu mengurangi redundansi data dan meningkatkan integritas data.</p><h2>Indexing Strategy</h2><p>Index yang tepat dapat meningkatkan performa query secara signifikan.</p><h2>Relationship Design</h2><p>Pahami kapan menggunakan one-to-one, one-to-many, dan many-to-many relationships.</p>',
 'Panduan lengkap merancang database yang efisien dan scalable.',
 'Database',
 'database, design, mysql, normalization',
 'published',
 987),

('Responsive Design dengan Tailwind CSS',
 '<h2>Mengapa Tailwind CSS?</h2><p>Tailwind CSS adalah utility-first CSS framework yang memungkinkan rapid development dengan konsistensi design.</p><h2>Responsive Utilities</h2><p>Tailwind menyediakan responsive utilities yang memudahkan pembuatan design responsive.</p><h2>Best Practices</h2><p>Tips dan trik untuk menggunakan Tailwind CSS secara efektif dalam project Anda.</p>',
 'Membuat website yang responsive menggunakan utility-first CSS framework.',
 'Web Development',
 'tailwind, css, responsive, frontend',
 'published',
 756);

-- Sample projects
INSERT INTO projects (title, description, technologies, project_url, github_url, featured) VALUES
('E-Commerce Website',
 'Full-stack e-commerce solution dengan fitur lengkap seperti shopping cart, payment gateway, dan admin panel untuk mengelola produk dan pesanan.',
 'PHP, MySQL, Bootstrap, JavaScript',
 'https://example.com/ecommerce',
 'https://github.com/username/ecommerce',
 TRUE),

('Task Management App',
 'Aplikasi manajemen tugas kolaboratif dengan real-time updates, notification system, dan team collaboration features.',
 'React, Node.js, MongoDB, Socket.io',
 'https://example.com/taskapp',
 'https://github.com/username/taskapp',
 TRUE),

('Learning Management System',
 'Platform pembelajaran online dengan fitur course management, progress tracking, quiz system, dan video streaming.',
 'Laravel, Vue.js, MySQL, Redis',
 'https://example.com/lms',
 'https://github.com/username/lms',
 TRUE);

-- Sample skills
INSERT INTO skills (name, category, level, icon, order_index) VALUES
('PHP', 'Backend', 'expert', 'fab fa-php', 1),
('JavaScript', 'Frontend', 'advanced', 'fab fa-js-square', 2),
('Python', 'Backend', 'intermediate', 'fab fa-python', 3),
('MySQL', 'Database', 'advanced', 'fas fa-database', 4),
('HTML5', 'Frontend', 'expert', 'fab fa-html5', 5),
('CSS3', 'Frontend', 'expert', 'fab fa-css3-alt', 6),
('React', 'Frontend', 'intermediate', 'fab fa-react', 7),
('Laravel', 'Backend', 'advanced', 'fab fa-laravel', 8),
('Git', 'Tools', 'advanced', 'fab fa-git-alt', 9),
('Docker', 'DevOps', 'intermediate', 'fab fa-docker', 10);

-- Sample experience
INSERT INTO experience (company, position, description, start_date, end_date, is_current, location, order_index) VALUES
('PT. Tech Solutions Indonesia', 'Senior Full Stack Developer', 
 'Memimpin tim developer dalam mengembangkan aplikasi enterprise. Bertanggung jawab dalam arsitektur sistem, code review, dan mentoring junior developer.',
 '2022-01-01', NULL, TRUE, 'Jakarta, Indonesia', 1),

('CV. Digital Creative', 'Junior Web Developer',
 'Mengembangkan website company profile dan e-commerce untuk berbagai client. Kolaborasi dengan tim design untuk implementasi UI/UX yang optimal.',
 '2021-01-01', '2021-12-31', FALSE, 'Jakarta, Indonesia', 2),

('Freelancer', 'Web Developer',
 'Mengerjakan berbagai project website untuk UMKM dan startup. Memberikan konsultasi teknologi dan solusi digital untuk meningkatkan bisnis client.',
 '2020-01-01', '2020-12-31', FALSE, 'Remote', 3);

-- Sample education
INSERT INTO education (institution, degree, field_of_study, start_date, end_date, gpa, description, order_index) VALUES
('Universitas Indonesia', 'Sarjana Komputer', 'Teknik Informatika',
 '2018-08-01', '2022-07-31', '3.75',
 'Fokus pada pengembangan software, algoritma, dan struktur data. Aktif dalam organisasi mahasiswa dan berbagai kompetisi programming.',
 1),

('SMA Negeri 1 Jakarta', 'SMA', 'IPA',
 '2015-07-01', '2018-06-30', NULL,
 'Jurusan IPA dengan fokus pada matematika dan fisika. Aktif dalam ekstrakurikuler programming dan olimpiade matematika.',
 2);

-- Create indexes for better performance
CREATE INDEX idx_articles_status ON articles(status);
CREATE INDEX idx_articles_category ON articles(category);
CREATE INDEX idx_articles_created_at ON articles(created_at);
CREATE INDEX idx_contact_messages_status ON contact_messages(status);
CREATE INDEX idx_projects_featured ON projects(featured);
CREATE INDEX idx_skills_category ON skills(category);
CREATE INDEX idx_experience_current ON experience(is_current);
CREATE INDEX idx_education_current ON education(is_current);
