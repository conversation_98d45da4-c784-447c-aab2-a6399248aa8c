<?php
// Database configuration for Freehostia.com hosting
// GANTI NILAI-NILAI INI DENGAN INFORMASI DARI FREEHOSTIA PANEL

define('DB_HOST', 'mysql.freehostia.com'); // Hostname Freehostia
define('DB_USER', 'username_portfolio_user'); // Ganti 'username' dengan username And<PERSON>
define('DB_PASS', 'your_database_password'); // Password database yang Anda buat
define('DB_NAME', 'username_portfolio'); // Ganti 'username' dengan username Anda

// Database connection class optimized for Freehostia
class Database {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;
    private $pdo;

    public function getConnection() {
        $this->pdo = null;
        try {
            // Connection options optimized for shared hosting
            $options = array(
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4",
                PDO::ATTR_PERSISTENT => false, // Disable persistent connections
                PDO::ATTR_TIMEOUT => 30, // Connection timeout
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
            );

            $this->pdo = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->dbname . ";charset=utf8mb4",
                $this->user,
                $this->pass,
                $options
            );
        } catch(PDOException $exception) {
            // Log error for debugging (don't show to users)
            error_log("Database connection error: " . $exception->getMessage());
            
            // Show user-friendly error
            die("Database connection failed. Please try again later.");
        }
        return $this->pdo;
    }
}

// Create database instance
$database = new Database();
$pdo = $database->getConnection();
?>
