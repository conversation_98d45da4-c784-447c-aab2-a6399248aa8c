<?php
session_start();
include_once 'config/database.php';
include_once 'includes/functions.php';

// Get article ID
$article_id = $_GET['id'] ?? null;

if (!$article_id) {
    redirect('index.php?page=articles');
}

// Get article details
$article = get_article_by_id($article_id);

if (!$article) {
    redirect('index.php?page=articles');
}

// Update view count
try {
    $stmt = $pdo->prepare("UPDATE articles SET views = views + 1 WHERE id = :id");
    $stmt->bindParam(':id', $article_id);
    $stmt->execute();
} catch(PDOException $e) {
    // Ignore error
}

$page_title = $article['title'] . ' - Portfolio';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars(truncate_text(strip_tags($article['content']), 160)); ?>">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Poppins', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
                    },
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen font-sans">
    <!-- Navigation -->
    <?php include 'includes/navigation.php'; ?>
    
    <!-- Article Content -->
    <main class="py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center space-x-2 text-sm text-gray-600">
                    <li><a href="index.php" class="hover:text-primary">Home</a></li>
                    <li><i class="fas fa-chevron-right text-xs"></i></li>
                    <li><a href="index.php?page=articles" class="hover:text-primary">Articles</a></li>
                    <li><i class="fas fa-chevron-right text-xs"></i></li>
                    <li class="text-gray-900"><?php echo htmlspecialchars($article['title']); ?></li>
                </ol>
            </nav>
            
            <!-- Article Header -->
            <header class="mb-8">
                <div class="flex items-center gap-4 mb-4">
                    <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">
                        <?php echo htmlspecialchars($article['category'] ?? 'General'); ?>
                    </span>
                    <span class="text-gray-500 text-sm">
                        <?php echo format_date($article['created_at']); ?>
                    </span>
                    <span class="text-gray-500 text-sm">
                        <i class="fas fa-eye mr-1"></i>
                        <?php echo number_format($article['views'] ?? 0); ?> views
                    </span>
                </div>
                
                <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    <?php echo htmlspecialchars($article['title']); ?>
                </h1>
                
                <?php if (!empty($article['excerpt'])): ?>
                    <p class="text-xl text-gray-600 leading-relaxed">
                        <?php echo htmlspecialchars($article['excerpt']); ?>
                    </p>
                <?php endif; ?>
            </header>
            
            <!-- Featured Image -->
            <?php if (!empty($article['image'])): ?>
                <div class="mb-8">
                    <img src="assets/images/articles/<?php echo htmlspecialchars($article['image']); ?>" 
                         alt="<?php echo htmlspecialchars($article['title']); ?>" 
                         class="w-full h-64 lg:h-96 object-cover rounded-lg shadow-lg">
                </div>
            <?php endif; ?>
            
            <!-- Article Content -->
            <article class="prose prose-lg max-w-none">
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <?php echo $article['content']; ?>
                </div>
            </article>
            
            <!-- Article Footer -->
            <footer class="mt-8 pt-8 border-t border-gray-200">
                <!-- Tags -->
                <?php if (!empty($article['tags'])): ?>
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Tags:</h3>
                        <div class="flex flex-wrap gap-2">
                            <?php 
                            $tags = explode(',', $article['tags']);
                            foreach ($tags as $tag): 
                                $tag = trim($tag);
                                if (!empty($tag)):
                            ?>
                                <span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 transition-colors">
                                    #<?php echo htmlspecialchars($tag); ?>
                                </span>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Share Buttons -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Bagikan Artikel:</h3>
                    <div class="flex gap-3">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>" 
                           target="_blank"
                           class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fab fa-facebook-f"></i>
                            Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>&text=<?php echo urlencode($article['title']); ?>" 
                           target="_blank"
                           class="flex items-center gap-2 px-4 py-2 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors">
                            <i class="fab fa-twitter"></i>
                            Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>" 
                           target="_blank"
                           class="flex items-center gap-2 px-4 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                            LinkedIn
                        </a>
                        <button onclick="copyToClipboard()" 
                                class="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-link"></i>
                            Copy Link
                        </button>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="flex flex-col sm:flex-row justify-between gap-4">
                    <a href="index.php?page=articles" 
                       class="flex items-center gap-2 text-primary hover:text-secondary font-semibold">
                        <i class="fas fa-arrow-left"></i>
                        Kembali ke Articles
                    </a>
                    <a href="index.php?page=contact" 
                       class="flex items-center gap-2 text-primary hover:text-secondary font-semibold">
                        Hubungi Saya
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </footer>
        </div>
    </main>
    
    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <script>
        function copyToClipboard() {
            navigator.clipboard.writeText(window.location.href).then(function() {
                alert('Link berhasil disalin!');
            });
        }
    </script>
</body>
</html>
