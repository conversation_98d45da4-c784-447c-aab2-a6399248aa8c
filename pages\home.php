<!-- Hero Section with Photo and Biodata -->
<section class="bg-gradient-to-r from-primary to-secondary text-white py-12 sm:py-16 lg:py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center animate-on-scroll">
            <!-- Photo -->
            <div class="text-center lg:text-left order-2 lg:order-1">
                <div class="relative inline-block">
                    <img src="assets/images/prof_g.jpg"
                         alt="Profile Photo"
                         class="w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64 rounded-full object-cover border-4 border-white shadow-lg mx-auto lg:mx-0 transition-transform duration-300 hover:scale-105"
                         onerror="this.src='https://via.placeholder.com/256x256/3B82F6/FFFFFF?text=Photo'">
                    <div class="absolute inset-0 rounded-full bg-gradient-to-tr from-transparent to-white opacity-20"></div>
                </div>
            </div>
            
            <!-- Biodata -->
            <div class="order-1 lg:order-2 text-center lg:text-left">
                <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 animate-fade-in">Muhammad Gezza Riyan Try Asmara</h1>
                <h2 class="text-lg sm:text-xl lg:text-2xl text-blue-200 mb-6 animate-fade-in">Web Developer dan UI/UX Designer</h2>
                <p class="text-base sm:text-lg text-blue-100 mb-8 leading-relaxed animate-fade-in">
                    seorang mahasiswa universitas maritim raja ali haji , fakultas teknik dan teknologi kemaritiman, program studi teknik informatika yang berfokus pada pembuatan tampilan web yang menarik, responsif, dan mudah digunakan. Saya senang menggabungkan logika pemrograman dengan kreativitas desain untuk menciptakan pengalaman digital yang optimal.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-fade-in">
                    <a href="index.php?page=contact"
                       class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1">
                        Contact Me
                    </a>
                    <a href="#projects"
                       class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1">
                        View Projects
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Skills Section -->
<section class="py-12 sm:py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 animate-on-scroll">
            <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Skills & Technologies</h2>
            <p class="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base">
                Bahasa pemrograman dan teknologi yang saya kuasai
            </p>
        </div>

        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6 animate-on-scroll">
            <!-- Programming Languages -->
            <div class="text-center p-3 sm:p-4 bg-gradient-to-br from-purple-500 to-purple-700 rounded-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <i class="fab fa-php text-3xl sm:text-4xl text-white mb-2 group-hover:scale-110 transition-transform duration-300 relative z-10"></i>
                <h3 class="font-semibold text-white text-sm sm:text-base relative z-10">PHP</h3>
            </div>
            <div class="text-center p-3 sm:p-4 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <i class="fab fa-js-square text-3xl sm:text-4xl text-white mb-2 group-hover:scale-110 transition-transform duration-300 relative z-10"></i>
                <h3 class="font-semibold text-white text-sm sm:text-base relative z-10">JavaScript</h3>
            </div>
            <div class="text-center p-3 sm:p-4 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <i class="fab fa-python text-3xl sm:text-4xl text-white mb-2 group-hover:scale-110 transition-transform duration-300 relative z-10"></i>
                <h3 class="font-semibold text-white text-sm sm:text-base relative z-10">Python</h3>
            </div>
            <div class="text-center p-3 sm:p-4 bg-gradient-to-br from-red-500 to-red-700 rounded-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <i class="fab fa-java text-3xl sm:text-4xl text-white mb-2 group-hover:scale-110 transition-transform duration-300 relative z-10"></i>
                <h3 class="font-semibold text-white text-sm sm:text-base relative z-10">Java</h3>
            </div>
            <div class="text-center p-3 sm:p-4 bg-gradient-to-br from-orange-500 to-orange-700 rounded-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <i class="fab fa-html5 text-3xl sm:text-4xl text-white mb-2 group-hover:scale-110 transition-transform duration-300 relative z-10"></i>
                <h3 class="font-semibold text-white text-sm sm:text-base relative z-10">HTML5</h3>
            </div>
            <div class="text-center p-3 sm:p-4 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <i class="fab fa-css3-alt text-3xl sm:text-4xl text-white mb-2 group-hover:scale-110 transition-transform duration-300 relative z-10"></i>
                <h3 class="font-semibold text-white text-sm sm:text-base relative z-10">CSS3</h3>
            </div>
        </div>
    </div>
</section>

<!-- Projects Section -->
<section id="projects" class="py-12 sm:py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 animate-on-scroll">
            <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Featured Projects</h2>
            <p class="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base">
                Beberapa project yang telah saya kerjakan
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 animate-on-scroll">
            <!-- Project 1 -->
            <div class="bg-gradient-to-br from-white to-blue-50 rounded-lg shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 group border border-blue-100">
                <div class="relative overflow-hidden">
                    <img src="assets/images/project1.jpg"
                         alt="Project 1"
                         class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                         onerror="this.src='https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=Project+1'">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="p-6 relative">
                    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">E-Commerce Website</h3>
                    <p class="text-gray-600 mb-4">
                        Full-stack e-commerce solution with payment integration and admin panel.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-sm rounded-full shadow-sm">PHP</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-sm rounded-full shadow-sm">MySQL</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-sm rounded-full shadow-sm">Bootstrap</span>
                    </div>
                    <a href="#" class="inline-flex items-center text-primary hover:text-secondary font-semibold group-hover:translate-x-1 transition-all duration-300">
                        View Project
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
            
            <!-- Project 2 -->
            <div class="bg-gradient-to-br from-white to-green-50 rounded-lg shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 group border border-green-100">
                <div class="relative overflow-hidden">
                    <img src="assets/images/project2.jpg"
                         alt="Project 2"
                         class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                         onerror="this.src='https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=Project+2'">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="p-6 relative">
                    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-teal-500"></div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">Task Management App</h3>
                    <p class="text-gray-600 mb-4">
                        Collaborative task management application with real-time updates.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-gradient-to-r from-cyan-500 to-cyan-600 text-white text-sm rounded-full shadow-sm">React</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-sm rounded-full shadow-sm">Node.js</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white text-sm rounded-full shadow-sm">MongoDB</span>
                    </div>
                    <a href="#" class="inline-flex items-center text-primary hover:text-secondary font-semibold group-hover:translate-x-1 transition-all duration-300">
                        View Project
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
            
            <!-- Project 3 -->
            <div class="bg-gradient-to-br from-white to-purple-50 rounded-lg shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 group border border-purple-100">
                <div class="relative overflow-hidden">
                    <img src="assets/images/project3.jpg"
                         alt="Project 3"
                         class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                         onerror="this.src='https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=Project+3'">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="p-6 relative">
                    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500"></div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">Learning Management System</h3>
                    <p class="text-gray-600 mb-4">
                        Online learning platform with course management and progress tracking.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-sm rounded-full shadow-sm">Laravel</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white text-sm rounded-full shadow-sm">Vue.js</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-red-600 to-red-700 text-white text-sm rounded-full shadow-sm">Redis</span>
                    </div>
                    <a href="#" class="inline-flex items-center text-primary hover:text-secondary font-semibold group-hover:translate-x-1 transition-all duration-300">
                        View Project
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Education Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Education</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Riwayat pendidikan formal dan sertifikasi
            </p>
        </div>

        <div class="space-y-8">
            <!-- Education Item 1 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-semibold rounded-full text-sm shadow-sm">
                        2018 - 2022
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">S1 Teknik Informatika</h3>
                    <p class="text-gray-700 mb-2 font-medium">Universitas Indonesia</p>
                    <p class="text-gray-600">
                        Fokus pada pengembangan software, algoritma, dan struktur data.
                        IPK: 3.75/4.00
                    </p>
                </div>
            </div>

            <!-- Education Item 2 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold rounded-full text-sm shadow-sm">
                        2015 - 2018
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">SMA Negeri 1 Jakarta</h3>
                    <p class="text-gray-700 mb-2 font-medium">Jurusan IPA</p>
                    <p class="text-gray-600">
                        Aktif dalam ekstrakurikuler programming dan olimpiade matematika.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Experience Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Work Experience</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Pengalaman kerja dan magang di berbagai perusahaan
            </p>
        </div>

        <div class="space-y-8">
            <!-- Experience Item 1 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-white to-blue-50 rounded-lg shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold rounded-full text-sm shadow-md">
                        <i class="fas fa-briefcase mr-2"></i>
                        2022 - Present
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">Senior Full Stack Developer</h3>
                    <p class="text-gray-700 mb-3 font-medium">PT. Tech Solutions Indonesia</p>
                    <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mengembangkan aplikasi web menggunakan PHP, Laravel, dan React</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Memimpin tim developer dalam project enterprise</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mengoptimalkan performa database dan aplikasi</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mentoring junior developer</li>
                    </ul>
                </div>
            </div>

            <!-- Experience Item 2 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-white to-green-50 rounded-lg shadow-lg border border-green-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-green-500 to-teal-500 text-white font-semibold rounded-full text-sm shadow-md">
                        <i class="fas fa-code mr-2"></i>
                        2021 - 2022
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">Junior Web Developer</h3>
                    <p class="text-gray-700 mb-3 font-medium">CV. Digital Creative</p>
                    <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mengembangkan website company profile dan e-commerce</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Maintenance dan update website client</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Kolaborasi dengan tim design untuk implementasi UI/UX</li>
                    </ul>
                </div>
            </div>

            <!-- Experience Item 3 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-white to-purple-50 rounded-lg shadow-lg border border-purple-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-full text-sm shadow-md">
                        <i class="fas fa-laptop mr-2"></i>
                        2020 - 2021
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">Freelance Web Developer</h3>
                    <p class="text-gray-700 mb-3 font-medium">Freelancer</p>
                    <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mengerjakan project website untuk UMKM dan startup</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Membuat sistem informasi sederhana</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Konsultasi teknologi untuk client</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Organization Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Organization</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Organisasi dan komunitas yang pernah saya ikuti
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Organization Item 1 -->
            <div class="p-6 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg border border-blue-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">Himpunan Mahasiswa Teknik Informatika</h3>
                        <p class="text-gray-600 font-medium">2019 - 2021</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-2"><strong>Posisi:</strong> <span class="text-blue-600 font-semibold">Ketua Divisi IT</span></p>
                <p class="text-gray-600">
                    Bertanggung jawab mengembangkan website organisasi dan sistem informasi internal.
                </p>
            </div>

            <!-- Organization Item 2 -->
            <div class="p-6 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg border border-green-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-code text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors">Jakarta Developer Community</h3>
                        <p class="text-gray-600 font-medium">2020 - Present</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-2"><strong>Posisi:</strong> <span class="text-green-600 font-semibold">Core Member</span></p>
                <p class="text-gray-600">
                    Aktif dalam kegiatan sharing session dan workshop teknologi terbaru.
                </p>
            </div>

            <!-- Organization Item 3 -->
            <div class="p-6 bg-gradient-to-br from-purple-50 to-pink-100 rounded-lg border border-purple-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-graduation-cap text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">Google Developer Student Club</h3>
                        <p class="text-gray-600 font-medium">2020 - 2022</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-2"><strong>Posisi:</strong> <span class="text-purple-600 font-semibold">Lead</span></p>
                <p class="text-gray-600">
                    Memimpin komunitas mahasiswa dalam belajar teknologi Google dan mengadakan event.
                </p>
            </div>

            <!-- Organization Item 4 -->
            <div class="p-6 bg-gradient-to-br from-red-50 to-pink-100 rounded-lg border border-red-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-heart text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-red-600 transition-colors">Relawan IT untuk Indonesia</h3>
                        <p class="text-gray-600 font-medium">2021 - Present</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-2"><strong>Posisi:</strong> <span class="text-red-600 font-semibold">Volunteer</span></p>
                <p class="text-gray-600">
                    Membantu digitalisasi UMKM dan memberikan pelatihan teknologi gratis.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Activity Section -->
<section class="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 animate-on-scroll">
            <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4">Activities & Achievements</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Kegiatan dan pencapaian yang telah saya raih
            </p>
        </div>

        <div class="space-y-6">
            <!-- Activity Item 1 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-yellow-50 to-orange-100 rounded-lg shadow-lg border border-yellow-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-400 to-orange-500"></div>
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-bold rounded-full shadow-md group-hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        2023
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-yellow-600 transition-colors">
                        <div class="inline-flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mr-3 shadow-md group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-trophy text-white text-lg"></i>
                            </div>
                            Juara 1 Hackathon Nasional
                        </div>
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Memenangkan kompetisi hackathon dengan tema "Smart City Solution"
                        dengan mengembangkan aplikasi manajemen sampah berbasis IoT.
                    </p>
                    <div class="mt-3 inline-flex items-center px-3 py-1 bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-700 text-sm rounded-full border border-yellow-200">
                        <i class="fas fa-medal mr-2"></i>
                        First Place Winner
                    </div>
                </div>
            </div>

            <!-- Activity Item 2 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-lg border border-blue-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-indigo-500"></div>
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-bold rounded-full shadow-md group-hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        2022
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                        <div class="inline-flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center mr-3 shadow-md group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-microphone text-white text-lg"></i>
                            </div>
                            Speaker di TechTalk Indonesia
                        </div>
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Berbagi pengalaman tentang "Modern Web Development with PHP"
                        di hadapan 500+ developer se-Indonesia.
                    </p>
                    <div class="mt-3 inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm rounded-full border border-blue-200">
                        <i class="fas fa-users mr-2"></i>
                        500+ Attendees
                    </div>
                </div>
            </div>

            <!-- Activity Item 3 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg shadow-lg border border-green-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-emerald-500"></div>
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-bold rounded-full shadow-md group-hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        2021
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
                        <div class="inline-flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-3 shadow-md group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-certificate text-white text-lg"></i>
                            </div>
                            AWS Certified Solutions Architect
                        </div>
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Memperoleh sertifikasi AWS untuk kemampuan merancang dan
                        mengimplementasikan sistem terdistribusi di cloud.
                    </p>
                    <div class="mt-3 inline-flex items-center px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-sm rounded-full border border-green-200">
                        <i class="fas fa-cloud mr-2"></i>
                        Cloud Architecture
                    </div>
                </div>
            </div>

            <!-- Activity Item 4 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-purple-50 to-pink-100 rounded-lg shadow-lg border border-purple-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-pink-500"></div>
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold rounded-full shadow-md group-hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        2020
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">
                        <div class="inline-flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center mr-3 shadow-md group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-book text-white text-lg"></i>
                            </div>
                            Publikasi Paper Ilmiah
                        </div>
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Mempublikasikan paper tentang "Machine Learning untuk Prediksi Cuaca"
                        di jurnal internasional bereputasi.
                    </p>
                    <div class="mt-3 inline-flex items-center px-3 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-sm rounded-full border border-purple-200">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        International Journal
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
