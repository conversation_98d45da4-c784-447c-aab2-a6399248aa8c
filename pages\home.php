<!-- Hero Section with Photo and Biodata -->
<section class="bg-gradient-to-br from-primary via-indigo-600 to-secondary text-white py-12 sm:py-16 lg:py-20 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-indigo-800/20"></div>
    <div class="absolute top-0 left-0 w-full h-full">
        <div class="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
        <div class="absolute top-32 right-20 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
        <div class="absolute bottom-20 left-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center animate-on-scroll">
            <!-- Photo -->
            <div class="text-center lg:text-left order-2 lg:order-1">
                <div class="relative inline-block group">
                    <!-- Gradient Ring Background -->
                    <div class="absolute -inset-4 bg-gradient-to-r from-white/30 via-blue-200/40 to-indigo-300/30 rounded-full blur-lg group-hover:blur-xl transition-all duration-500"></div>

                    <!-- Photo Container -->
                    <div class="relative">
                        <img src="assets/images/prof_g.jpg"
                             alt="Profile Photo"
                             class="w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64 rounded-full object-cover border-4 border-white/80 shadow-2xl mx-auto lg:mx-0 transition-all duration-500 hover:scale-105 hover:border-white relative z-10"
                             onerror="this.src='https://via.placeholder.com/256x256/3B82F6/FFFFFF?text=Photo'">

                        <!-- Gradient Overlay -->
                        <div class="absolute inset-0 rounded-full bg-gradient-to-tr from-transparent via-white/10 to-white/20 group-hover:from-white/5 group-hover:to-white/25 transition-all duration-500"></div>

                        <!-- Floating Elements -->
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full shadow-lg animate-pulse"></div>
                        <div class="absolute -bottom-3 -left-3 w-4 h-4 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full shadow-lg animate-pulse delay-1000"></div>
                    </div>
                </div>
            </div>
            
            <!-- Biodata -->
            <div class="order-1 lg:order-2 text-center lg:text-left relative">
                <!-- Background Card -->
                <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 rounded-2xl backdrop-blur-sm border border-white/20 shadow-2xl"></div>

                <div class="relative z-10 p-6 lg:p-8">
                    <!-- Name with Gradient Text -->
                    <div class="mb-6">
                        <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-2 animate-fade-in">
                            <span class="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                                Muhammad Gezza Riyan Try Asmara
                            </span>
                        </h1>

                        <!-- Role with Enhanced Styling -->
                        <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-white/20 to-blue-200/30 rounded-full border border-white/30 backdrop-blur-sm mb-4">
                            <div class="w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mr-3 animate-pulse"></div>
                            <h2 class="text-lg sm:text-xl lg:text-2xl font-semibold text-white animate-fade-in">
                                Web Developer & UI/UX Designer
                            </h2>
                        </div>
                    </div>
                <p class="text-base sm:text-lg text-blue-100 mb-8 leading-relaxed animate-fade-in">
                    seorang mahasiswa universitas maritim raja ali haji , fakultas teknik dan teknologi kemaritiman, program studi teknik informatika yang berfokus pada pembuatan tampilan web yang menarik, responsif, dan mudah digunakan. Saya senang menggabungkan logika pemrograman dengan kreativitas desain untuk menciptakan pengalaman digital yang optimal.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-fade-in">
                    <a href="index.php?page=contact"
                       class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1">
                        Contact Me
                    </a>
                    <a href="#projects"
                       class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1">
                        View Projects
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Skills Section -->
<section class="py-12 sm:py-16 bg-gradient-to-br from-white via-gray-50 to-blue-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-blue-200/20 rounded-full blur-3xl"></div>
        <div class="absolute top-32 right-20 w-40 h-40 bg-purple-200/15 rounded-full blur-3xl"></div>
        <div class="absolute bottom-20 left-1/3 w-36 h-36 bg-indigo-200/20 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Enhanced Header -->
        <div class="text-center mb-16 animate-on-scroll">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-indigo-600 rounded-full shadow-lg mb-6">
                <i class="fas fa-code text-white text-2xl"></i>
            </div>
            <h2 class="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 via-primary to-indigo-600 bg-clip-text text-transparent mb-4">
                Skills & Technologies
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base mb-8">
                Bahasa pemrograman dan teknologi yang saya kuasai
            </p>

            <!-- Skill Level Indicator -->
            <div class="flex justify-center items-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
                    <span class="text-gray-600">Expert</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full"></div>
                    <span class="text-gray-600">Advanced</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"></div>
                    <span class="text-gray-600">Intermediate</span>
                </div>
            </div>
        </div>

        <!-- Skills Grid with Categories -->
        <div class="space-y-12 animate-on-scroll">
            <!-- Programming Languages -->
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-code text-white text-sm"></i>
                    </div>
                    Programming Languages
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6">
                    <!-- PHP - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-purple-500 to-purple-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-purple-400/30">
                            <!-- Shimmer Effect -->
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <!-- Skill Level Indicator -->
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-php text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">PHP</h4>
                            <p class="text-purple-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>

                    <!-- JavaScript - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-yellow-300/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-js-square text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">JavaScript</h4>
                            <p class="text-yellow-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Python - Intermediate -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-python text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Python</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Intermediate</p>
                        </div>
                    </div>

                    <!-- Java - Intermediate -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-red-500 to-red-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-red-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-java text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Java</h4>
                            <p class="text-red-200 text-xs mt-1 relative z-10">Intermediate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Frontend Technologies -->
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-paint-brush text-white text-sm"></i>
                    </div>
                    Frontend Technologies
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6">
                    <!-- HTML5 - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-orange-500 to-orange-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-orange-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-html5 text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">HTML5</h4>
                            <p class="text-orange-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>

                    <!-- CSS3 - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-300/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-css3-alt text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">CSS3</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>

                    <!-- React - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-cyan-300/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-react text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">React</h4>
                            <p class="text-cyan-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Tailwind CSS - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-teal-400 to-teal-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-teal-300/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fas fa-wind text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Tailwind</h4>
                            <p class="text-teal-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backend & Database -->
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-server text-white text-sm"></i>
                    </div>
                    Backend & Database
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6">
                    <!-- MySQL - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-500/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fas fa-database text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">MySQL</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Laravel - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-red-500 to-red-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-red-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-laravel text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Laravel</h4>
                            <p class="text-red-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Node.js - Intermediate -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-green-600 to-green-800 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-green-500/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-node-js text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Node.js</h4>
                            <p class="text-green-200 text-xs mt-1 relative z-10">Intermediate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tools & Others -->
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-gray-600 to-gray-800 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-tools text-white text-sm"></i>
                    </div>
                    Tools & Others
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6">
                    <!-- Git - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-orange-600 to-red-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-orange-500/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-git-alt text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Git</h4>
                            <p class="text-orange-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Figma - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-purple-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-figma text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Figma</h4>
                            <p class="text-purple-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- VS Code - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fas fa-code text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">VS Code</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>

                    <!-- Photoshop - Intermediate -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-600 to-cyan-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-500/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full shadow-lg"></div>
                            <i class="fas fa-image text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Photoshop</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Intermediate</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Skills Summary -->
        <div class="mt-16 bg-gradient-to-br from-white to-blue-50 rounded-2xl p-8 border border-blue-100 shadow-xl">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-4">
                    Skills Summary
                </h3>
                <p class="text-gray-600">Ringkasan kemampuan dan pengalaman teknologi</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Frontend Development -->
                <div class="text-center p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl border border-orange-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-paint-brush text-white text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Frontend Development</h4>
                    <p class="text-gray-600 text-sm mb-4">Membuat tampilan web yang menarik dan responsif</p>
                    <div class="flex justify-center space-x-2">
                        <span class="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">HTML5</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">CSS3</span>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full">JavaScript</span>
                    </div>
                </div>

                <!-- Backend Development -->
                <div class="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-server text-white text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Backend Development</h4>
                    <p class="text-gray-600 text-sm mb-4">Membangun logika server dan database</p>
                    <div class="flex justify-center space-x-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">PHP</span>
                        <span class="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">Laravel</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">MySQL</span>
                    </div>
                </div>

                <!-- UI/UX Design -->
                <div class="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-palette text-white text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">UI/UX Design</h4>
                    <p class="text-gray-600 text-sm mb-4">Merancang pengalaman pengguna yang optimal</p>
                    <div class="flex justify-center space-x-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">Figma</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Photoshop</span>
                        <span class="px-2 py-1 bg-teal-100 text-teal-700 text-xs rounded-full">Tailwind</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Section -->
<section id="projects" class="py-12 sm:py-16 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-40 h-40 bg-blue-200/20 rounded-full blur-3xl"></div>
        <div class="absolute top-40 right-20 w-56 h-56 bg-purple-200/15 rounded-full blur-3xl"></div>
        <div class="absolute bottom-32 left-1/3 w-48 h-48 bg-indigo-200/20 rounded-full blur-3xl"></div>
        <div class="absolute bottom-10 right-10 w-32 h-32 bg-pink-200/15 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Enhanced Header -->
        <div class="text-center mb-16 animate-on-scroll">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full shadow-lg mb-6">
                <i class="fas fa-rocket text-white text-2xl"></i>
            </div>
            <h2 class="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 via-primary to-purple-600 bg-clip-text text-transparent mb-4">
                Featured Projects
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base mb-8">
                Beberapa project yang telah saya kerjakan dengan teknologi terkini
            </p>

            <!-- Project Categories -->
            <div class="flex justify-center items-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
                    <span class="text-gray-600">Web Application</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-full"></div>
                    <span class="text-gray-600">Algorithm</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                    <span class="text-gray-600">System</span>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 animate-on-scroll">
            <!-- Project 1 - InnovaKick -->
            <div class="group relative">
                <div class="bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-4 border border-blue-200/50 relative">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Project Category Badge -->
                    <div class="absolute top-4 left-4 z-20">
                        <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs font-semibold rounded-full shadow-lg">
                            E-Commerce
                        </span>
                    </div>

                    <!-- Featured Badge -->
                    <div class="absolute top-4 right-4 z-20">
                        <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                            <i class="fas fa-star text-white text-sm"></i>
                        </div>
                    </div>

                    <!-- Image Container -->
                    <div class="relative overflow-hidden h-56">
                        <img src="assets/images/project1.jpg"
                             alt="InnovaKick Project"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                             onerror="this.src='https://via.placeholder.com/400x250/3B82F6/FFFFFF?text=InnovaKick'">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <!-- Floating Action Button -->
                        <div class="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                            <button class="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors">
                                <i class="fas fa-external-link-alt text-primary"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-6 relative">
                        <!-- Top Gradient Border -->
                        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500"></div>

                        <!-- Project Title -->
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                                InnovaKick
                            </h3>
                            <div class="flex items-center gap-1 text-yellow-400">
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                            </div>
                        </div>

                        <!-- Project Description -->
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Website penjualan sepatu aplikasi InnovaKick yang telah dikembangkan dan dipublikasikan secara daring dengan fitur lengkap e-commerce.
                        </p>

                        <!-- Technology Stack -->
                        <div class="mb-6">
                            <h4 class="text-sm font-semibold text-gray-700 mb-2">Tech Stack:</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">PHP</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">MySQL</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">Bootstrap</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">JavaScript</span>
                            </div>
                        </div>

                        <!-- Project Stats -->
                        <div class="grid grid-cols-3 gap-4 mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                            <div class="text-center">
                                <div class="text-lg font-bold text-blue-600">100+</div>
                                <div class="text-xs text-gray-600">Products</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-green-600">50+</div>
                                <div class="text-xs text-gray-600">Orders</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-purple-600">5★</div>
                                <div class="text-xs text-gray-600">Rating</div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex gap-3">
                            <a href="#" class="flex-1 bg-gradient-to-r from-primary to-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:from-blue-600 hover:to-indigo-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 text-center">
                                <i class="fas fa-eye mr-2"></i>Live Demo
                            </a>
                            <a href="#" class="flex-1 border-2 border-primary text-primary py-3 px-4 rounded-lg font-semibold hover:bg-primary hover:text-white transition-all duration-300 text-center">
                                <i class="fab fa-github mr-2"></i>Code
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project 2 - PAA Algorithm -->
            <div class="group relative">
                <div class="bg-gradient-to-br from-white via-green-50 to-emerald-100 rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-4 border border-green-200/50 relative">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Project Category Badge -->
                    <div class="absolute top-4 left-4 z-20">
                        <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-teal-500 text-white text-xs font-semibold rounded-full shadow-lg">
                            Algorithm
                        </span>
                    </div>

                    <!-- Innovation Badge -->
                    <div class="absolute top-4 right-4 z-20">
                        <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                            <i class="fas fa-lightbulb text-white text-sm"></i>
                        </div>
                    </div>

                    <!-- Image Container -->
                    <div class="relative overflow-hidden h-56">
                        <img src="assets/images/project2.jpg"
                             alt="PAA Algorithm Project"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                             onerror="this.src='https://via.placeholder.com/400x250/10B981/FFFFFF?text=PAA+Algorithm'">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <!-- Floating Action Button -->
                        <div class="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                            <button class="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors">
                                <i class="fas fa-external-link-alt text-green-600"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-6 relative">
                        <!-- Top Gradient Border -->
                        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 via-teal-500 to-emerald-500"></div>

                        <!-- Project Title -->
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-xl font-bold text-gray-900 group-hover:text-green-600 transition-colors">
                                PAA Algorithm
                            </h3>
                            <div class="flex items-center gap-1 text-green-400">
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="far fa-star text-sm"></i>
                            </div>
                        </div>

                        <!-- Project Description -->
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Denah ini dibuat untuk mengimplementasikan logika algoritma dalam membentuk tata letak kota yang terstruktur dan efisien menggunakan pendekatan algoritmik.
                        </p>

                        <!-- Technology Stack -->
                        <div class="mb-6">
                            <h4 class="text-sm font-semibold text-gray-700 mb-2">Tech Stack:</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">Python</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">Node.js</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">HTML</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">Algorithm</span>
                            </div>
                        </div>

                        <!-- Project Stats -->
                        <div class="grid grid-cols-3 gap-4 mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100">
                            <div class="text-center">
                                <div class="text-lg font-bold text-green-600">95%</div>
                                <div class="text-xs text-gray-600">Efficiency</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-teal-600">1000+</div>
                                <div class="text-xs text-gray-600">Nodes</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-emerald-600">O(n)</div>
                                <div class="text-xs text-gray-600">Complexity</div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex gap-3">
                            <a href="#" class="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 px-4 rounded-lg font-semibold hover:from-emerald-600 hover:to-teal-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 text-center">
                                <i class="fas fa-play mr-2"></i>Run Demo
                            </a>
                            <a href="#" class="flex-1 border-2 border-green-500 text-green-600 py-3 px-4 rounded-lg font-semibold hover:bg-green-500 hover:text-white transition-all duration-300 text-center">
                                <i class="fab fa-github mr-2"></i>Code
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project 3 - Masyarakat Cerdas -->
            <div class="group relative">
                <div class="bg-gradient-to-br from-white via-purple-50 to-pink-100 rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-4 border border-purple-200/50 relative">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Project Category Badge -->
                    <div class="absolute top-4 left-4 z-20">
                        <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-semibold rounded-full shadow-lg">
                            Government System
                        </span>
                    </div>

                    <!-- Community Badge -->
                    <div class="absolute top-4 right-4 z-20">
                        <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                            <i class="fas fa-users text-white text-sm"></i>
                        </div>
                    </div>

                    <!-- Image Container -->
                    <div class="relative overflow-hidden h-56">
                        <img src="assets/images/project3.jpg"
                             alt="Masyarakat Cerdas Project"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                             onerror="this.src='https://via.placeholder.com/400x250/8B5CF6/FFFFFF?text=Masyarakat+Cerdas'">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <!-- Floating Action Button -->
                        <div class="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                            <button class="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors">
                                <i class="fas fa-external-link-alt text-purple-600"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-6 relative">
                        <!-- Top Gradient Border -->
                        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-purple-600"></div>

                        <!-- Project Title -->
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors">
                                Masyarakat Cerdas
                            </h3>
                            <div class="flex items-center gap-1 text-purple-400">
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                            </div>
                        </div>

                        <!-- Project Description -->
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Website untuk administrasi desa secara online di kelurahan Sei. Enam yang memudahkan masyarakat dalam mengurus dokumen dan layanan publik.
                        </p>

                        <!-- Technology Stack -->
                        <div class="mb-6">
                            <h4 class="text-sm font-semibold text-gray-700 mb-2">Tech Stack:</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">Laravel</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">Vue.js</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">MySQL</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full shadow-sm hover:scale-105 transition-transform">Bootstrap</span>
                            </div>
                        </div>

                        <!-- Project Stats -->
                        <div class="grid grid-cols-3 gap-4 mb-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100">
                            <div class="text-center">
                                <div class="text-lg font-bold text-purple-600">500+</div>
                                <div class="text-xs text-gray-600">Citizens</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-pink-600">20+</div>
                                <div class="text-xs text-gray-600">Services</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-indigo-600">98%</div>
                                <div class="text-xs text-gray-600">Satisfaction</div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex gap-3">
                            <a href="#" class="flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 px-4 rounded-lg font-semibold hover:from-pink-600 hover:to-purple-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 text-center">
                                <i class="fas fa-globe mr-2"></i>Visit Site
                            </a>
                            <a href="#" class="flex-1 border-2 border-purple-500 text-purple-600 py-3 px-4 rounded-lg font-semibold hover:bg-purple-500 hover:text-white transition-all duration-300 text-center">
                                <i class="fas fa-info-circle mr-2"></i>Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Projects Summary & CTA -->
        <div class="mt-16 bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl p-8 border border-blue-200/50 shadow-xl relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-4 left-4 w-16 h-16 bg-primary rounded-full"></div>
                <div class="absolute top-8 right-8 w-12 h-12 bg-purple-500 rounded-full"></div>
                <div class="absolute bottom-6 left-8 w-20 h-20 bg-indigo-500 rounded-full"></div>
                <div class="absolute bottom-4 right-4 w-14 h-14 bg-pink-500 rounded-full"></div>
            </div>

            <div class="relative z-10 text-center">
                <div class="mb-8">
                    <h3 class="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 via-primary to-purple-600 bg-clip-text text-transparent mb-4">
                        Interested in My Work?
                    </h3>
                    <p class="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base">
                        Saya selalu terbuka untuk proyek baru dan kolaborasi menarik. Mari diskusikan ide Anda!
                    </p>
                </div>

                <!-- Project Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                    <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border border-blue-200">
                        <div class="text-2xl font-bold text-blue-600 mb-1">10+</div>
                        <div class="text-sm text-gray-600">Projects Completed</div>
                    </div>
                    <div class="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border border-green-200">
                        <div class="text-2xl font-bold text-green-600 mb-1">5+</div>
                        <div class="text-sm text-gray-600">Technologies</div>
                    </div>
                    <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl border border-purple-200">
                        <div class="text-2xl font-bold text-purple-600 mb-1">100%</div>
                        <div class="text-sm text-gray-600">Client Satisfaction</div>
                    </div>
                    <div class="text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl border border-orange-200">
                        <div class="text-2xl font-bold text-orange-600 mb-1">24/7</div>
                        <div class="text-sm text-gray-600">Support</div>
                    </div>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="index.php?page=contact"
                       class="group bg-gradient-to-r from-primary to-purple-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-purple-600 hover:to-indigo-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i class="fas fa-envelope mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                        Start a Project
                    </a>
                    <a href="index.php?page=articles"
                       class="group border-2 border-primary text-primary px-8 py-4 rounded-xl font-semibold hover:bg-primary hover:text-white transition-all duration-300 hover:shadow-lg">
                        <i class="fas fa-blog mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                        Read My Blog
                    </a>
                    <a href="#"
                       class="group bg-gradient-to-r from-gray-600 to-gray-800 text-white px-8 py-4 rounded-xl font-semibold hover:from-gray-800 hover:to-black transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i class="fab fa-github mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                        View GitHub
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Education Section -->
<section class="py-16 bg-gradient-to-br from-white via-gray-50 to-blue-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-40 h-40 bg-blue-200/20 rounded-full blur-3xl"></div>
        <div class="absolute top-40 right-20 w-56 h-56 bg-purple-200/15 rounded-full blur-3xl"></div>
        <div class="absolute bottom-32 left-1/3 w-48 h-48 bg-indigo-200/20 rounded-full blur-3xl"></div>
        <div class="absolute bottom-10 right-10 w-32 h-32 bg-green-200/15 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Enhanced Header -->
        <div class="text-center mb-16">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-indigo-600 rounded-full shadow-lg mb-6">
                <i class="fas fa-graduation-cap text-white text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-primary to-indigo-600 bg-clip-text text-transparent mb-4">
                Education Journey
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto mb-8">
                Perjalanan pendidikan formal dari SD hingga Perguruan Tinggi
            </p>

            <!-- Education Level Indicators -->
            <div class="flex justify-center items-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
                    <span class="text-gray-600">Perguruan Tinggi</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                    <span class="text-gray-600">SMA</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                    <span class="text-gray-600">SMP</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                    <span class="text-gray-600">SD</span>
                </div>
            </div>
        </div>

        <!-- Education Timeline -->
        <div class="relative">
            <!-- Timeline Line -->
            <div class="absolute left-8 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 via-green-500 via-purple-500 to-orange-500 rounded-full hidden md:block"></div>

            <div class="space-y-12">
                <!-- Perguruan Tinggi -->
                <div class="group relative">
                    <div class="flex flex-col md:flex-row gap-6 p-8 bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl shadow-xl border border-blue-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                        <!-- Shimmer Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                        <!-- Timeline Dot -->
                        <div class="absolute -left-4 top-8 w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full border-4 border-white shadow-lg hidden md:flex items-center justify-center">
                            <i class="fas fa-university text-white text-xs"></i>
                        </div>

                        <!-- Level Badge -->
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-xs font-semibold rounded-full shadow-lg">
                                Perguruan Tinggi
                            </span>
                        </div>

                        <div class="md:w-1/4 relative z-10">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2022 - Sekarang
                            </div>
                            <div class="text-blue-600 font-semibold text-sm">Status: Aktif</div>
                        </div>

                        <div class="md:w-3/4 relative z-10">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                                S1 Teknik Informatika
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">Universitas Maritim Raja Ali Haji</p>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Fokus pada pengembangan software, algoritma, struktur data, dan teknologi web modern.
                                Aktif dalam organisasi mahasiswa dan berbagai project pengembangan aplikasi.
                            </p>

                            <!-- Achievements -->
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg border border-blue-100">
                                    <div class="text-lg font-bold text-blue-600">3.75</div>
                                    <div class="text-xs text-gray-600">IPK</div>
                                </div>
                                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg border border-green-100">
                                    <div class="text-lg font-bold text-green-600">10+</div>
                                    <div class="text-xs text-gray-600">Projects</div>
                                </div>
                                <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg border border-purple-100">
                                    <div class="text-lg font-bold text-purple-600">5+</div>
                                    <div class="text-xs text-gray-600">Certificates</div>
                                </div>
                            </div>

                            <!-- Skills Gained -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full">Web Development</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full">Database Design</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full">Algorithm</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs rounded-full">UI/UX Design</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SMA -->
                <div class="group relative">
                    <div class="flex flex-col md:flex-row gap-6 p-8 bg-gradient-to-br from-white via-green-50 to-emerald-100 rounded-2xl shadow-xl border border-green-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                        <!-- Shimmer Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                        <!-- Timeline Dot -->
                        <div class="absolute -left-4 top-8 w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full border-4 border-white shadow-lg hidden md:flex items-center justify-center">
                            <i class="fas fa-school text-white text-xs"></i>
                        </div>

                        <!-- Level Badge -->
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-semibold rounded-full shadow-lg">
                                SMA
                            </span>
                        </div>

                        <div class="md:w-1/4 relative z-10">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2019 - 2022
                            </div>
                            <div class="text-green-600 font-semibold text-sm">Status: Lulus</div>
                        </div>

                        <div class="md:w-3/4 relative z-10">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
                                SMA Negeri 4 Tanjungpinang
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">Jurusan IPA (Ilmu Pengetahuan Alam)</p>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Fokus pada mata pelajaran sains dan matematika. Aktif dalam ekstrakurikuler Futsal dan Sepak Bola
                            </p>

                            <!-- Achievements -->
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg border border-green-100">
                                    <div class="text-lg font-bold text-green-600">85.5</div>
                                    <div class="text-xs text-gray-600">Rata-rata</div>
                                </div>
                                <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-lg border border-blue-100">
                                    <div class="text-lg font-bold text-blue-600">Top 10</div>
                                    <div class="text-xs text-gray-600">Ranking</div>
                                </div>
                                <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-3 rounded-lg border border-purple-100">
                                    <div class="text-lg font-bold text-purple-600">3+</div>
                                    <div class="text-xs text-gray-600">Ekstrakurikuler</div>
                                </div>
                            </div>

                            <!-- Activities -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full">Futsal</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full">OSIS</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full">Rohis</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs rounded-full">IT Competition</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SMP -->
                <div class="group relative">
                    <div class="flex flex-col md:flex-row gap-6 p-8 bg-gradient-to-br from-white via-purple-50 to-pink-100 rounded-2xl shadow-xl border border-purple-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                        <!-- Shimmer Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                        <!-- Timeline Dot -->
                        <div class="absolute -left-4 top-8 w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full border-4 border-white shadow-lg hidden md:flex items-center justify-center">
                            <i class="fas fa-book text-white text-xs"></i>
                        </div>

                        <!-- Level Badge -->
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-semibold rounded-full shadow-lg">
                                SMP
                            </span>
                        </div>

                        <div class="md:w-1/4 relative z-10">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2016 - 2019
                            </div>
                            <div class="text-purple-600 font-semibold text-sm">Status: Lulus</div>
                        </div>

                        <div class="md:w-3/4 relative z-10">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">
                                 SMP Negeri 12 Tanjungpinang Timur
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">Pendidikan Menengah Pertama</p>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Masa pembentukan dasar akademik yang kuat dengan fokus pada matematika dan sains.
                                Mulai tertarik dengan dunia teknologi dan komputer.
                            </p>

                            <!-- Achievements -->
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg border border-purple-100">
                                    <div class="text-lg font-bold text-purple-600">88.2</div>
                                    <div class="text-xs text-gray-600">Rata-rata</div>
                                </div>
                                <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-lg border border-blue-100">
                                    <div class="text-lg font-bold text-blue-600">Top 5</div>
                                    <div class="text-xs text-gray-600">Ranking</div>
                                </div>
                                <div class="bg-gradient-to-r from-green-50 to-green-100 p-3 rounded-lg border border-green-100">
                                    <div class="text-lg font-bold text-green-600">2+</div>
                                    <div class="text-xs text-gray-600">Prestasi</div>
                                </div>
                            </div>

                            <!-- Activities -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full">Matematika</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-pink-500 to-pink-600 text-white text-xs rounded-full">Sains</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full">Komputer</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SD -->
                <div class="group relative">
                    <div class="flex flex-col md:flex-row gap-6 p-8 bg-gradient-to-br from-white via-orange-50 to-red-100 rounded-2xl shadow-xl border border-orange-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                        <!-- Shimmer Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                        <!-- Timeline Dot -->
                        <div class="absolute -left-4 top-8 w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-full border-4 border-white shadow-lg hidden md:flex items-center justify-center">
                            <i class="fas fa-child text-white text-xs"></i>
                        </div>

                        <!-- Level Badge -->
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-semibold rounded-full shadow-lg">
                                SD
                            </span>
                        </div>

                        <div class="md:w-1/4 relative z-10">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-red-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2010 - 2016
                            </div>
                            <div class="text-orange-600 font-semibold text-sm">Status: Lulus</div>
                        </div>

                        <div class="md:w-3/4 relative z-10">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors">
                                SD Negeri 003 Tanjungpinang Timur
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">Pendidikan Dasar</p>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Fondasi pendidikan yang kuat dengan prestasi akademik yang baik.
                                Mulai menunjukkan minat pada futsal dan kegiatan kreatif.
                            </p>

                            <!-- Achievements -->
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                <div class="bg-gradient-to-r from-orange-50 to-red-50 p-3 rounded-lg border border-orange-100">
                                    <div class="text-lg font-bold text-orange-600">90.5</div>
                                    <div class="text-xs text-gray-600">Rata-rata</div>
                                </div>
                                <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 p-3 rounded-lg border border-yellow-100">
                                    <div class="text-lg font-bold text-yellow-600">Top 3</div>
                                    <div class="text-xs text-gray-600">Ranking</div>
                                </div>
                                <div class="bg-gradient-to-r from-green-50 to-green-100 p-3 rounded-lg border border-green-100">
                                    <div class="text-lg font-bold text-green-600">5+</div>
                                    <div class="text-xs text-gray-600">Penghargaan</div>
                                </div>
                            </div>

                            <!-- Activities -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs rounded-full">Matematika</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full">Bahasa Indonesia</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white text-xs rounded-full">Seni</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full">Olahraga</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Education Summary -->
        <div class="mt-16 bg-gradient-to-br from-white to-blue-50 rounded-2xl p-8 border border-blue-100 shadow-xl">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-4">
                    Education Summary
                </h3>
                <p class="text-gray-600">Perjalanan pendidikan yang konsisten dengan prestasi akademik yang baik</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Total Years -->
                <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">16+ Tahun</h4>
                    <p class="text-gray-600 text-sm">Total Pendidikan</p>
                </div>

                <!-- Academic Achievement -->
                <div class="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-trophy text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Prestasi Tinggi</h4>
                    <p class="text-gray-600 text-sm">Akademik Konsisten</p>
                </div>

                <!-- Skills Developed -->
                <div class="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-brain text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Multi Skills</h4>
                    <p class="text-gray-600 text-sm">Teknologi & Sains</p>
                </div>

                <!-- Current Status -->
                <div class="text-center p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl border border-orange-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-graduation-cap text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Mahasiswa Aktif</h4>
                    <p class="text-gray-600 text-sm">Semester 7</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Experience Section -->
<section class="py-16 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-40 h-40 bg-blue-200/20 rounded-full blur-3xl"></div>
        <div class="absolute top-40 right-20 w-56 h-56 bg-purple-200/15 rounded-full blur-3xl"></div>
        <div class="absolute bottom-32 left-1/3 w-48 h-48 bg-indigo-200/20 rounded-full blur-3xl"></div>
        <div class="absolute bottom-10 right-10 w-32 h-32 bg-green-200/15 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Enhanced Header -->
        <div class="text-center mb-16">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full shadow-lg mb-6">
                <i class="fas fa-cocktail text-white text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-primary to-purple-600 bg-clip-text text-transparent mb-4">
                Work Experience
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto mb-8">
                Pengalaman kerja sebagai bartender di berbagai restoran dan cafe
            </p>

            <!-- Experience Categories -->
            <div class="flex justify-center items-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
                    <span class="text-gray-600">Current Position</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                    <span class="text-gray-600">Previous Experience</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                    <span class="text-gray-600">Bartender</span>
                </div>
            </div>
        </div>

        <!-- Experience Timeline -->
        <div class="relative">
            <!-- Timeline Line -->
            <div class="absolute left-8 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 via-green-500 via-purple-500 to-orange-500 rounded-full hidden md:block"></div>

            <div class="space-y-12">
                <!-- Current Position - Hayashi Restoran Japanese -->
                <div class="group relative">
                    <div class="flex flex-col md:flex-row gap-6 p-8 bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl shadow-xl border border-blue-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                        <!-- Shimmer Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                        <!-- Timeline Dot -->
                        <div class="absolute -left-4 top-8 w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full border-4 border-white shadow-lg hidden md:flex items-center justify-center">
                            <i class="fas fa-cocktail text-white text-xs"></i>
                        </div>

                        <!-- Current Badge -->
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-semibold rounded-full shadow-lg animate-pulse">
                                Current Position
                            </span>
                        </div>

                        <div class="md:w-1/4 relative z-10">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2024 - 2025
                            </div>
                            <div class="text-blue-600 font-semibold text-sm">Status: Active</div>
                        </div>

                        <div class="md:w-3/4 relative z-10">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                                Bartender
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">Hayashi Restoran Japanese</p>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Bertanggung jawab dalam pembuatan minuman tradisional Jepang dan cocktail modern,
                                melayani pelanggan dengan standar pelayanan tinggi di restoran Japanese premium.
                            </p>

                            <!-- Responsibilities -->
                            <ul class="text-gray-600 space-y-2 mb-6">
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Membuat minuman tradisional Jepang (sake, shochu, tea ceremony)</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Menyajikan cocktail dan mocktail dengan presentasi premium</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Memberikan rekomendasi minuman sesuai menu makanan</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Menjaga kebersihan dan standar hygiene bar area</li>
                            </ul>

                            <!-- Skills -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full">Japanese Drinks</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full">Cocktail Making</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full">Customer Service</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs rounded-full">Premium Service</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Little Krabi -->
                <div class="group relative">
                    <div class="flex flex-col md:flex-row gap-6 p-8 bg-gradient-to-br from-white via-green-50 to-emerald-100 rounded-2xl shadow-xl border border-green-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                        <!-- Shimmer Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                        <!-- Timeline Dot -->
                        <div class="absolute -left-4 top-8 w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full border-4 border-white shadow-lg hidden md:flex items-center justify-center">
                            <i class="fas fa-glass-martini text-white text-xs"></i>
                        </div>

                        <div class="md:w-1/4 relative z-10">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2023 - 2024
                            </div>
                            <div class="text-green-600 font-semibold text-sm">Duration: 1 Year</div>
                        </div>

                        <div class="md:w-3/4 relative z-10">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
                                Bartender
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">Little Krabi</p>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Mengembangkan keahlian bartending di restoran Thai dengan fokus pada minuman
                                tradisional Thailand dan cocktail tropical yang eksotis.
                            </p>

                            <!-- Responsibilities -->
                            <ul class="text-gray-600 space-y-2 mb-6">
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Membuat minuman tradisional Thailand dan cocktail tropical</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Mengelola inventory bar dan kontrol kualitas minuman</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Melatih staff baru dalam teknik bartending</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Mengembangkan menu minuman seasonal</li>
                            </ul>

                            <!-- Skills -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full">Thai Beverages</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-teal-500 to-teal-600 text-white text-xs rounded-full">Tropical Cocktails</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full">Inventory Management</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full">Staff Training</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ayam Geprek Tanjungpinang -->
                <div class="group relative">
                    <div class="flex flex-col md:flex-row gap-6 p-8 bg-gradient-to-br from-white via-purple-50 to-pink-100 rounded-2xl shadow-xl border border-purple-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                        <!-- Shimmer Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                        <!-- Timeline Dot -->
                        <div class="absolute -left-4 top-8 w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full border-4 border-white shadow-lg hidden md:flex items-center justify-center">
                            <i class="fas fa-wine-glass text-white text-xs"></i>
                        </div>

                        <div class="md:w-1/4 relative z-10">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2023 - 2023
                            </div>
                            <div class="text-purple-600 font-semibold text-sm">Duration: 6 Months</div>
                        </div>

                        <div class="md:w-3/4 relative z-10">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">
                                Bartender
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">Ayam Geprek Tanjungpinang</p>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Mengembangkan kemampuan bartending di restoran casual dining dengan fokus pada
                                minuman segar dan jus buah yang cocok dengan menu makanan pedas.
                            </p>

                            <!-- Responsibilities -->
                            <ul class="text-gray-600 space-y-2 mb-6">
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Membuat minuman segar dan jus buah untuk menu casual dining</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Menyajikan minuman yang cocok dengan makanan pedas</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Mengelola operasional bar dengan tempo yang cepat</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Memberikan pelayanan ramah untuk target keluarga</li>
                            </ul>

                            <!-- Skills -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full">Fresh Beverages</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-pink-500 to-pink-600 text-white text-xs rounded-full">Juice Making</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs rounded-full">Fast Service</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full">Family Service</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Seoul Cafe -->
                <div class="group relative">
                    <div class="flex flex-col md:flex-row gap-6 p-8 bg-gradient-to-br from-white via-orange-50 to-red-100 rounded-2xl shadow-xl border border-orange-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                        <!-- Shimmer Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                        <!-- Timeline Dot -->
                        <div class="absolute -left-4 top-8 w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-full border-4 border-white shadow-lg hidden md:flex items-center justify-center">
                            <i class="fas fa-coffee text-white text-xs"></i>
                        </div>

                        <!-- First Job Badge -->
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-semibold rounded-full shadow-lg">
                                First Experience
                            </span>
                        </div>

                        <div class="md:w-1/4 relative z-10">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-red-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2021 - 2023
                            </div>
                            <div class="text-orange-600 font-semibold text-sm">Duration: 2 Years</div>
                        </div>

                        <div class="md:w-3/4 relative z-10">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors">
                                Bartender
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">Seoul Cafe</p>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Memulai karir sebagai bartender di cafe Korean dengan mempelajari dasar-dasar
                                bartending, coffee making, dan pelayanan pelanggan yang berkualitas.
                            </p>

                            <!-- Responsibilities -->
                            <ul class="text-gray-600 space-y-2 mb-6">
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Mempelajari dasar-dasar bartending dan coffee making</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Membuat minuman Korea tradisional dan modern coffee</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Mengembangkan skill customer service dan komunikasi</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Belajar manajemen waktu dan kerja tim</li>
                            </ul>

                            <!-- Skills -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs rounded-full">Coffee Making</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full">Korean Drinks</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white text-xs rounded-full">Basic Bartending</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full">Team Work</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Experience Summary -->
        <div class="mt-16 bg-gradient-to-br from-white to-blue-50 rounded-2xl p-8 border border-blue-100 shadow-xl">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-4">
                    Experience Summary
                </h3>
                <p class="text-gray-600">Perjalanan karir sebagai bartender dengan pengalaman di berbagai jenis restoran</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Total Experience -->
                <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">4+ Years</h4>
                    <p class="text-gray-600 text-sm">Total Experience</p>
                </div>

                <!-- Restaurants Worked -->
                <div class="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-utensils text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">4 Restaurants</h4>
                    <p class="text-gray-600 text-sm">Different Cuisines</p>
                </div>

                <!-- Skills Mastered -->
                <div class="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-cocktail text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Multi Cuisine</h4>
                    <p class="text-gray-600 text-sm">Bartending Skills</p>
                </div>

                <!-- Current Status -->
                <div class="text-center p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl border border-orange-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-star text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Expert Level</h4>
                    <p class="text-gray-600 text-sm">Current Skill</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Organization Section -->
<section class="py-16 bg-gradient-to-br from-white via-gray-50 to-blue-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-40 h-40 bg-blue-200/20 rounded-full blur-3xl"></div>
        <div class="absolute top-40 right-20 w-56 h-56 bg-purple-200/15 rounded-full blur-3xl"></div>
        <div class="absolute bottom-32 left-1/3 w-48 h-48 bg-indigo-200/20 rounded-full blur-3xl"></div>
        <div class="absolute bottom-10 right-10 w-32 h-32 bg-green-200/15 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Enhanced Header -->
        <div class="text-center mb-16">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full shadow-lg mb-6">
                <i class="fas fa-users text-white text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-primary to-purple-600 bg-clip-text text-transparent mb-4">
                Organization & Leadership
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto mb-8">
                Organisasi mahasiswa dan komunitas yang aktif saya ikuti dengan berbagai posisi kepemimpinan
            </p>

            <!-- Organization Types -->
            <div class="flex justify-center items-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
                    <span class="text-gray-600">Student Organization</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                    <span class="text-gray-600">E-Sport Community</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                    <span class="text-gray-600">Executive Board</span>
                </div>
            </div>
        </div>

        <!-- Organization Cards -->
        <div class="space-y-8">
            <!-- BEM FTTK - Current Position -->
            <div class="group relative">
                <div class="p-8 bg-gradient-to-br from-white via-purple-50 to-pink-100 rounded-2xl shadow-xl border border-purple-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Current Badge -->
                    <div class="absolute top-4 right-4">
                        <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-semibold rounded-full shadow-lg animate-pulse">
                            Current Position
                        </span>
                    </div>

                    <div class="flex flex-col md:flex-row gap-6 relative z-10">
                        <div class="md:w-1/4">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2025 - 2026
                            </div>
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-university text-white text-2xl"></i>
                            </div>
                        </div>

                        <div class="md:w-3/4">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">
                                Badan Eksekutif Mahasiswa Fakultas Teknik dan Teknologi Kemaritiman
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">(BEM FTTK)</p>
                            <div class="mb-4">
                                <span class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-semibold border border-purple-200">
                                    <i class="fas fa-crown mr-2"></i>
                                    Koordinator Sinergritas Bidang PSDM
                                </span>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Bertanggung jawab dalam koordinasi sinergritas bidang Pengembangan Sumber Daya Manusia,
                                mengelola program pengembangan soft skill dan hard skill mahasiswa fakultas.
                            </p>

                            <!-- Responsibilities -->
                            <ul class="text-gray-600 space-y-2 mb-6">
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Koordinasi program pengembangan SDM mahasiswa</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Mengelola pelatihan soft skill dan hard skill</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Sinergritas antar bidang dalam BEM FTTK</li>
                            </ul>

                            <!-- Skills -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full">Leadership</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-pink-500 to-pink-600 text-white text-xs rounded-full">Coordination</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white text-xs rounded-full">HR Development</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full">Team Management</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- RAHEC - E-Sport Community -->
            <div class="group relative">
                <div class="p-8 bg-gradient-to-br from-white via-green-50 to-emerald-100 rounded-2xl shadow-xl border border-green-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Leader Badge -->
                    <div class="absolute top-4 right-4">
                        <span class="px-3 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs font-semibold rounded-full shadow-lg">
                            Community Leader
                        </span>
                    </div>

                    <div class="flex flex-col md:flex-row gap-6 relative z-10">
                        <div class="md:w-1/4">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2024 - 2025
                            </div>
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-gamepad text-white text-2xl"></i>
                            </div>
                        </div>

                        <div class="md:w-3/4">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
                                Raja Ali Haji E-Sport Community
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">(RAHEC)</p>
                            <div class="mb-4">
                                <span class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 rounded-full text-sm font-semibold border border-green-200">
                                    <i class="fas fa-trophy mr-2"></i>
                                    Ketua
                                </span>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Memimpin komunitas e-sport universitas dalam mengembangkan bakat gaming mahasiswa,
                                menyelenggarakan turnamen, dan membangun ekosistem e-sport yang sehat di kampus.
                            </p>

                            <!-- Responsibilities -->
                            <ul class="text-gray-600 space-y-2 mb-6">
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Memimpin dan mengelola komunitas e-sport kampus</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Menyelenggarakan turnamen dan kompetisi gaming</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Mengembangkan bakat gaming mahasiswa</li>
                            </ul>

                            <!-- Skills -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full">Community Management</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white text-xs rounded-full">Event Organization</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-teal-500 to-teal-600 text-white text-xs rounded-full">Gaming Strategy</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full">Team Building</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- HMTI - Student Organization -->
            <div class="group relative">
                <div class="p-8 bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl shadow-xl border border-blue-200/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Multi-year Badge -->
                    <div class="absolute top-4 right-4">
                        <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-xs font-semibold rounded-full shadow-lg">
                            2 Years Experience
                        </span>
                    </div>

                    <div class="flex flex-col md:flex-row gap-6 relative z-10">
                        <div class="md:w-1/4">
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-bold rounded-xl text-sm shadow-lg mb-4">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                2023 - 2025
                            </div>
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-laptop-code text-white text-2xl"></i>
                            </div>
                        </div>

                        <div class="md:w-3/4">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                                Himpunan Mahasiswa Teknik Informatika
                            </h3>
                            <p class="text-lg text-gray-700 mb-3 font-semibold">(HMTI)</p>
                            <div class="mb-4">
                                <span class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 rounded-full text-sm font-semibold border border-blue-200">
                                    <i class="fas fa-star mr-2"></i>
                                    Kadiv Aspirasi Minat dan Bakat
                                </span>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                Memimpin divisi yang bertanggung jawab menampung aspirasi mahasiswa dan mengembangkan
                                minat bakat di bidang teknologi informasi, programming, dan soft skill.
                            </p>

                            <!-- Responsibilities -->
                            <ul class="text-gray-600 space-y-2 mb-6">
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Mengelola program pengembangan minat dan bakat mahasiswa</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Menampung dan menyalurkan aspirasi mahasiswa TI</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Mengorganisir workshop dan pelatihan teknologi</li>
                                <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-3 mt-1 text-sm"></i>Koordinasi dengan divisi lain dalam HMTI</li>
                            </ul>

                            <!-- Skills -->
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full">Student Leadership</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white text-xs rounded-full">Program Management</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs rounded-full">Talent Development</span>
                                <span class="px-3 py-1 bg-gradient-to-r from-cyan-500 to-cyan-600 text-white text-xs rounded-full">Communication</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organization Summary -->
        <div class="mt-16 bg-gradient-to-br from-white to-blue-50 rounded-2xl p-8 border border-blue-100 shadow-xl">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-4">
                    Leadership Summary
                </h3>
                <p class="text-gray-600">Pengalaman kepemimpinan di berbagai organisasi mahasiswa dan komunitas</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Total Organizations -->
                <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">3 Organizations</h4>
                    <p class="text-gray-600 text-sm">Active Involvement</p>
                </div>

                <!-- Leadership Positions -->
                <div class="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-crown text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Leadership Roles</h4>
                    <p class="text-gray-600 text-sm">Coordinator & Chairman</p>
                </div>

                <!-- Experience Years -->
                <div class="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calendar text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">4+ Years</h4>
                    <p class="text-gray-600 text-sm">Organization Experience</p>
                </div>

                <!-- Skills Developed -->
                <div class="text-center p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl border border-orange-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-brain text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Soft Skills</h4>
                    <p class="text-gray-600 text-sm">Leadership & Management</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Activity Section -->
<section class="py-16 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-40 h-40 bg-blue-200/20 rounded-full blur-3xl"></div>
        <div class="absolute top-40 right-20 w-56 h-56 bg-purple-200/15 rounded-full blur-3xl"></div>
        <div class="absolute bottom-32 left-1/3 w-48 h-48 bg-indigo-200/20 rounded-full blur-3xl"></div>
        <div class="absolute bottom-10 right-10 w-32 h-32 bg-green-200/15 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Enhanced Header -->
        <div class="text-center mb-16">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full shadow-lg mb-6">
                <i class="fas fa-star text-white text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-primary to-purple-600 bg-clip-text text-transparent mb-4">
                Activities & Achievements
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto mb-8">
                Kegiatan kemahasiswaan, forum nasional, dan kompetisi yang telah saya ikuti
            </p>

            <!-- Activity Categories -->
            <div class="flex justify-center items-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
                    <span class="text-gray-600">Student Activities</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                    <span class="text-gray-600">National Forum</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                    <span class="text-gray-600">Competitions</span>
                </div>
            </div>
        </div>

        <!-- Activities by Category -->
        <div class="space-y-12">
            <!-- 1. Student Activities -->
            <div class="group relative">
                <div class="bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl shadow-xl border border-blue-200/50 p-8 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Category Header -->
                    <div class="flex items-center mb-8 relative z-10">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-4 shadow-lg">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-1">Kegiatan Kemahasiswaan</h3>
                            <p class="text-gray-600">Aktif dalam berbagai kegiatan dan kepanitiaan di kampus</p>
                        </div>
                    </div>

                    <!-- Activities Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 relative z-10">
                        <!-- LDK HMTI -->
                        <div class="p-6 bg-gradient-to-br from-white to-blue-50 rounded-xl border border-blue-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-graduation-cap text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-blue-600 transition-colors">LDK HMTI</h4>
                                    <p class="text-sm text-gray-600">2023 - 2024</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Latihan Dasar Kepemimpinan Himpunan Mahasiswa Teknik Informatika</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Peserta (2023)</span>
                                <span class="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full">Panitia (2024)</span>
                            </div>
                        </div>

                        <!-- Dies Natalis -->
                        <div class="p-6 bg-gradient-to-br from-white to-purple-50 rounded-xl border border-purple-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-birthday-cake text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-purple-600 transition-colors">Dies Natalis Informatics</h4>
                                    <p class="text-sm text-gray-600">2024</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Peringatan hari jadi Program Studi Teknik Informatika</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">Panitia</span>
                                <span class="px-2 py-1 bg-pink-100 text-pink-700 text-xs rounded-full">Event Organizer</span>
                            </div>
                        </div>

                        <!-- PKKMB -->
                        <div class="p-6 bg-gradient-to-br from-white to-green-50 rounded-xl border border-green-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-handshake text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-green-600 transition-colors">PKKMB Fakultas & Jurusan</h4>
                                    <p class="text-sm text-gray-600">2023 - 2024</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Pengenalan Kehidupan Kampus Mahasiswa Baru</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">Panitia 2023</span>
                                <span class="px-2 py-1 bg-emerald-100 text-emerald-700 text-xs rounded-full">Panitia 2024</span>
                            </div>
                        </div>

                        <!-- Engineering Festival -->
                        <div class="p-6 bg-gradient-to-br from-white to-orange-50 rounded-xl border border-orange-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-cogs text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-orange-600 transition-colors">Engineering Festival (E-Fest)</h4>
                                    <p class="text-sm text-gray-600">2023</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Festival tahunan Fakultas Teknik dan Teknologi Kemaritiman</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">Panitia</span>
                                <span class="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">Technical Team</span>
                            </div>
                        </div>

                        <!-- Film Production -->
                        <div class="p-6 bg-gradient-to-br from-white to-pink-50 rounded-xl border border-pink-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-pink-400 to-pink-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-video text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-pink-600 transition-colors">Film "Renjana" Fakultas Teknik</h4>
                                    <p class="text-sm text-gray-600">2023</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Produksi film dokumenter fakultas</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-pink-100 text-pink-700 text-xs rounded-full">Crew</span>
                                <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">Production Team</span>
                            </div>
                        </div>

                        <!-- Additional Activities -->
                        <div class="p-6 bg-gradient-to-br from-white to-gray-50 rounded-xl border border-gray-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-list text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-gray-600 transition-colors">Kegiatan Lainnya</h4>
                                    <p class="text-sm text-gray-600">2022 - 2024</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Berbagai kegiatan kemahasiswaan lainnya</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">Pemira Fakultas</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Rapat Senat</span>
                                <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">Dekan Cup</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2. Forum Nasional -->
            <div class="group relative">
                <div class="bg-gradient-to-br from-white via-green-50 to-emerald-100 rounded-2xl shadow-xl border border-green-200/50 p-8 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Category Header -->
                    <div class="flex items-center mb-8 relative z-10">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-4 shadow-lg">
                            <i class="fas fa-globe text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-1">Forum Nasional Mahasiswa Informatika</h3>
                            <p class="text-gray-600">Partisipasi dalam forum nasional PERMIKOMNAS</p>
                        </div>
                    </div>

                    <!-- Forum Activities -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 relative z-10">
                        <!-- Rakerwil VI -->
                        <div class="p-6 bg-gradient-to-br from-white to-green-50 rounded-xl border border-green-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-handshake text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-green-600 transition-colors">Rakerwil VI PERMIKOMNAS</h4>
                                    <p class="text-sm text-gray-600">2024</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Rapat Kerja Wilayah VI Perhimpunan Mahasiswa Ilmu Komputer Nasional Wilayah II</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">Peserta</span>
                                <span class="px-2 py-1 bg-emerald-100 text-emerald-700 text-xs rounded-full">Wilayah II</span>
                            </div>
                        </div>

                        <!-- Rakernas VIII -->
                        <div class="p-6 bg-gradient-to-br from-white to-emerald-50 rounded-xl border border-emerald-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-users text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-emerald-600 transition-colors">Rakernas VIII PERMIKOMNAS</h4>
                                    <p class="text-sm text-gray-600">2024</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Rapat Kerja Nasional VIII Perhimpunan Mahasiswa Ilmu Komputer Nasional</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-emerald-100 text-emerald-700 text-xs rounded-full">Peserta</span>
                                <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">Nasional</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 3. Kompetisi & Hobi -->
            <div class="group relative">
                <div class="bg-gradient-to-br from-white via-purple-50 to-pink-100 rounded-2xl shadow-xl border border-purple-200/50 p-8 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Category Header -->
                    <div class="flex items-center mb-8 relative z-10">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mr-4 shadow-lg">
                            <i class="fas fa-trophy text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-1">Kompetisi & Hobi</h3>
                            <p class="text-gray-600">Partisipasi dalam berbagai kompetisi olahraga dan e-sport</p>
                        </div>
                    </div>

                    <!-- Competition Activities -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 relative z-10">
                        <!-- Futsal -->
                        <div class="p-6 bg-gradient-to-br from-white to-purple-50 rounded-xl border border-purple-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-futbol text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-purple-600 transition-colors">Lomba Futsal</h4>
                                    <p class="text-sm text-gray-600">2022 - 2023</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Partisipasi dalam berbagai turnamen futsal di event kampus</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">Peserta</span>
                                <span class="px-2 py-1 bg-pink-100 text-pink-700 text-xs rounded-full">Team Sport</span>
                                <span class="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full">Campus Events</span>
                            </div>
                        </div>

                        <!-- Mobile Legends -->
                        <div class="p-6 bg-gradient-to-br from-white to-pink-50 rounded-xl border border-pink-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group/item">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-pink-400 to-pink-600 rounded-full flex items-center justify-center mr-3 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                                    <i class="fas fa-gamepad text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover/item:text-pink-600 transition-colors">Turnamen Mobile Legends</h4>
                                    <p class="text-sm text-gray-600">2022 - 2023</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Partisipasi dalam berbagai turnamen Mobile Legends internal dan eksternal</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-pink-100 text-pink-700 text-xs rounded-full">Peserta</span>
                                <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">E-Sport</span>
                                <span class="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">Gaming</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activities Summary -->
        <div class="mt-16 bg-gradient-to-br from-white to-blue-50 rounded-2xl p-8 border border-blue-100 shadow-xl">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-4">
                    Activities Summary
                </h3>
                <p class="text-gray-600">Ringkasan kegiatan dan pencapaian selama masa perkuliahan</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Student Activities -->
                <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">10+ Kegiatan</h4>
                    <p class="text-gray-600 text-sm">Kemahasiswaan</p>
                </div>

                <!-- National Forum -->
                <div class="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-globe text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">2 Forum</h4>
                    <p class="text-gray-600 text-sm">Nasional</p>
                </div>

                <!-- Competitions -->
                <div class="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-trophy text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Multiple</h4>
                    <p class="text-gray-600 text-sm">Competitions</p>
                </div>

                <!-- Years Active -->
                <div class="text-center p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl border border-orange-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calendar text-white"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">4+ Years</h4>
                    <p class="text-gray-600 text-sm">Active Period</p>
                </div>
            </div>
        </div>
    </div>
</section>
