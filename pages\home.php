<!-- Hero Section with Photo and Biodata -->
<section class="bg-gradient-to-br from-primary via-indigo-600 to-secondary text-white py-12 sm:py-16 lg:py-20 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-indigo-800/20"></div>
    <div class="absolute top-0 left-0 w-full h-full">
        <div class="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
        <div class="absolute top-32 right-20 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
        <div class="absolute bottom-20 left-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center animate-on-scroll">
            <!-- Photo -->
            <div class="text-center lg:text-left order-2 lg:order-1">
                <div class="relative inline-block group">
                    <!-- Gradient Ring Background -->
                    <div class="absolute -inset-4 bg-gradient-to-r from-white/30 via-blue-200/40 to-indigo-300/30 rounded-full blur-lg group-hover:blur-xl transition-all duration-500"></div>

                    <!-- Photo Container -->
                    <div class="relative">
                        <img src="assets/images/prof_g.jpg"
                             alt="Profile Photo"
                             class="w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64 rounded-full object-cover border-4 border-white/80 shadow-2xl mx-auto lg:mx-0 transition-all duration-500 hover:scale-105 hover:border-white relative z-10"
                             onerror="this.src='https://via.placeholder.com/256x256/3B82F6/FFFFFF?text=Photo'">

                        <!-- Gradient Overlay -->
                        <div class="absolute inset-0 rounded-full bg-gradient-to-tr from-transparent via-white/10 to-white/20 group-hover:from-white/5 group-hover:to-white/25 transition-all duration-500"></div>

                        <!-- Floating Elements -->
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full shadow-lg animate-pulse"></div>
                        <div class="absolute -bottom-3 -left-3 w-4 h-4 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full shadow-lg animate-pulse delay-1000"></div>
                    </div>
                </div>
            </div>
            
            <!-- Biodata -->
            <div class="order-1 lg:order-2 text-center lg:text-left relative">
                <!-- Background Card -->
                <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 rounded-2xl backdrop-blur-sm border border-white/20 shadow-2xl"></div>

                <div class="relative z-10 p-6 lg:p-8">
                    <!-- Name with Gradient Text -->
                    <div class="mb-6">
                        <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-2 animate-fade-in">
                            <span class="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                                Muhammad Gezza Riyan Try Asmara
                            </span>
                        </h1>

                        <!-- Role with Enhanced Styling -->
                        <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-white/20 to-blue-200/30 rounded-full border border-white/30 backdrop-blur-sm mb-4">
                            <div class="w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mr-3 animate-pulse"></div>
                            <h2 class="text-lg sm:text-xl lg:text-2xl font-semibold text-white animate-fade-in">
                                Web Developer & UI/UX Designer
                            </h2>
                        </div>
                    </div>
                <p class="text-base sm:text-lg text-blue-100 mb-8 leading-relaxed animate-fade-in">
                    seorang mahasiswa universitas maritim raja ali haji , fakultas teknik dan teknologi kemaritiman, program studi teknik informatika yang berfokus pada pembuatan tampilan web yang menarik, responsif, dan mudah digunakan. Saya senang menggabungkan logika pemrograman dengan kreativitas desain untuk menciptakan pengalaman digital yang optimal.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-fade-in">
                    <a href="index.php?page=contact"
                       class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1">
                        Contact Me
                    </a>
                    <a href="#projects"
                       class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1">
                        View Projects
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Skills Section -->
<section class="py-12 sm:py-16 bg-gradient-to-br from-white via-gray-50 to-blue-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-blue-200/20 rounded-full blur-3xl"></div>
        <div class="absolute top-32 right-20 w-40 h-40 bg-purple-200/15 rounded-full blur-3xl"></div>
        <div class="absolute bottom-20 left-1/3 w-36 h-36 bg-indigo-200/20 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Enhanced Header -->
        <div class="text-center mb-16 animate-on-scroll">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-indigo-600 rounded-full shadow-lg mb-6">
                <i class="fas fa-code text-white text-2xl"></i>
            </div>
            <h2 class="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 via-primary to-indigo-600 bg-clip-text text-transparent mb-4">
                Skills & Technologies
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base mb-8">
                Bahasa pemrograman dan teknologi yang saya kuasai
            </p>

            <!-- Skill Level Indicator -->
            <div class="flex justify-center items-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
                    <span class="text-gray-600">Expert</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full"></div>
                    <span class="text-gray-600">Advanced</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"></div>
                    <span class="text-gray-600">Intermediate</span>
                </div>
            </div>
        </div>

        <!-- Skills Grid with Categories -->
        <div class="space-y-12 animate-on-scroll">
            <!-- Programming Languages -->
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-code text-white text-sm"></i>
                    </div>
                    Programming Languages
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6">
                    <!-- PHP - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-purple-500 to-purple-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-purple-400/30">
                            <!-- Shimmer Effect -->
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <!-- Skill Level Indicator -->
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-php text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">PHP</h4>
                            <p class="text-purple-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>

                    <!-- JavaScript - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-yellow-300/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-js-square text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">JavaScript</h4>
                            <p class="text-yellow-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Python - Intermediate -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-python text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Python</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Intermediate</p>
                        </div>
                    </div>

                    <!-- Java - Intermediate -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-red-500 to-red-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-red-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-java text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Java</h4>
                            <p class="text-red-200 text-xs mt-1 relative z-10">Intermediate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Frontend Technologies -->
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-paint-brush text-white text-sm"></i>
                    </div>
                    Frontend Technologies
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6">
                    <!-- HTML5 - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-orange-500 to-orange-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-orange-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-html5 text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">HTML5</h4>
                            <p class="text-orange-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>

                    <!-- CSS3 - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-300/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-css3-alt text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">CSS3</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>

                    <!-- React - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-cyan-300/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-react text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">React</h4>
                            <p class="text-cyan-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Tailwind CSS - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-teal-400 to-teal-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-teal-300/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fas fa-wind text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Tailwind</h4>
                            <p class="text-teal-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backend & Database -->
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-server text-white text-sm"></i>
                    </div>
                    Backend & Database
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6">
                    <!-- MySQL - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-500/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fas fa-database text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">MySQL</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Laravel - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-red-500 to-red-700 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-red-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-laravel text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Laravel</h4>
                            <p class="text-red-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Node.js - Intermediate -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-green-600 to-green-800 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-green-500/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-node-js text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Node.js</h4>
                            <p class="text-green-200 text-xs mt-1 relative z-10">Intermediate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tools & Others -->
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-gray-600 to-gray-800 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-tools text-white text-sm"></i>
                    </div>
                    Tools & Others
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6">
                    <!-- Git - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-orange-600 to-red-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-orange-500/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-git-alt text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Git</h4>
                            <p class="text-orange-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- Figma - Advanced -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-purple-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-lg"></div>
                            <i class="fab fa-figma text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Figma</h4>
                            <p class="text-purple-200 text-xs mt-1 relative z-10">Advanced</p>
                        </div>
                    </div>

                    <!-- VS Code - Expert -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-400/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <i class="fas fa-code text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">VS Code</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Expert</p>
                        </div>
                    </div>

                    <!-- Photoshop - Intermediate -->
                    <div class="group relative">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-600 to-cyan-600 rounded-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 relative overflow-hidden border border-blue-500/30">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                            <div class="absolute top-2 right-2 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full shadow-lg"></div>
                            <i class="fas fa-image text-4xl text-white mb-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 relative z-10"></i>
                            <h4 class="font-bold text-white text-sm relative z-10">Photoshop</h4>
                            <p class="text-blue-200 text-xs mt-1 relative z-10">Intermediate</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Skills Summary -->
        <div class="mt-16 bg-gradient-to-br from-white to-blue-50 rounded-2xl p-8 border border-blue-100 shadow-xl">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-4">
                    Skills Summary
                </h3>
                <p class="text-gray-600">Ringkasan kemampuan dan pengalaman teknologi</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Frontend Development -->
                <div class="text-center p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl border border-orange-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-paint-brush text-white text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Frontend Development</h4>
                    <p class="text-gray-600 text-sm mb-4">Membuat tampilan web yang menarik dan responsif</p>
                    <div class="flex justify-center space-x-2">
                        <span class="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">HTML5</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">CSS3</span>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full">JavaScript</span>
                    </div>
                </div>

                <!-- Backend Development -->
                <div class="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-server text-white text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Backend Development</h4>
                    <p class="text-gray-600 text-sm mb-4">Membangun logika server dan database</p>
                    <div class="flex justify-center space-x-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">PHP</span>
                        <span class="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">Laravel</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">MySQL</span>
                    </div>
                </div>

                <!-- UI/UX Design -->
                <div class="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-palette text-white text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">UI/UX Design</h4>
                    <p class="text-gray-600 text-sm mb-4">Merancang pengalaman pengguna yang optimal</p>
                    <div class="flex justify-center space-x-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">Figma</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Photoshop</span>
                        <span class="px-2 py-1 bg-teal-100 text-teal-700 text-xs rounded-full">Tailwind</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Section -->
<section id="projects" class="py-12 sm:py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 animate-on-scroll">
            <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Featured Projects</h2>
            <p class="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base">
                Beberapa project yang telah saya kerjakan
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 animate-on-scroll">
            <!-- Project 1 -->
            <div class="bg-gradient-to-br from-white to-blue-50 rounded-lg shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 group border border-blue-100">
                <div class="relative overflow-hidden">
                    <img src="assets/images/project1.jpg"
                         alt="Project 1"
                         class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                         onerror="this.src='https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=Project+1'">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="p-6 relative">
                    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">InnovaKick</h3>
                    <p class="text-gray-600 mb-4">
                        Website penjualan sepatu aplikasi InovaKick yang telah dikembangkan dan dipublikasikan secara daring.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-sm rounded-full shadow-sm">PHP</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-sm rounded-full shadow-sm">MySQL</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-sm rounded-full shadow-sm">Bootstrap</span>
                    </div>
                    <a href="#" class="inline-flex items-center text-primary hover:text-secondary font-semibold group-hover:translate-x-1 transition-all duration-300">
                        View Project
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
            
            <!-- Project 2 -->
            <div class="bg-gradient-to-br from-white to-green-50 rounded-lg shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 group border border-green-100">
                <div class="relative overflow-hidden">
                    <img src="assets/images/project2.jpg"
                         alt="Project 2"
                         class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                         onerror="this.src='https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=Project+2'">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="p-6 relative">
                    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-teal-500"></div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">PAA</h3>
                    <p class="text-gray-600 mb-4">
                        Denah ini dibuat untuk mengimplementasikan logika algoritma dalam membentuk tata letak kota yang terstruktur dan efisien.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-gradient-to-r from-cyan-500 to-cyan-600 text-white text-sm rounded-full shadow-sm">Python</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-sm rounded-full shadow-sm">Node.js</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white text-sm rounded-full shadow-sm">HTML</span>
                    </div>
                    <a href="#" class="inline-flex items-center text-primary hover:text-secondary font-semibold group-hover:translate-x-1 transition-all duration-300">
                        View Project
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
            
            <!-- Project 3 -->
            <div class="bg-gradient-to-br from-white to-purple-50 rounded-lg shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 group border border-purple-100">
                <div class="relative overflow-hidden">
                    <img src="assets/images/project3.jpg"
                         alt="Project 3"
                         class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                         onerror="this.src='https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=Project+3'">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="p-6 relative">
                    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500"></div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">Masyarakat C</h3>
                    <p class="text-gray-600 mb-4">
                        Online learning platform with course management and progress tracking.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-sm rounded-full shadow-sm">Laravel</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white text-sm rounded-full shadow-sm">Vue.js</span>
                        <span class="px-3 py-1 bg-gradient-to-r from-red-600 to-red-700 text-white text-sm rounded-full shadow-sm">Redis</span>
                    </div>
                    <a href="#" class="inline-flex items-center text-primary hover:text-secondary font-semibold group-hover:translate-x-1 transition-all duration-300">
                        View Project
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Education Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Education</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Riwayat pendidikan formal dan sertifikasi
            </p>
        </div>

        <div class="space-y-8">
            <!-- Education Item 1 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-semibold rounded-full text-sm shadow-sm">
                        2018 - 2022
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">S1 Teknik Informatika</h3>
                    <p class="text-gray-700 mb-2 font-medium">Universitas Indonesia</p>
                    <p class="text-gray-600">
                        Fokus pada pengembangan software, algoritma, dan struktur data.
                        IPK: 3.75/4.00
                    </p>
                </div>
            </div>

            <!-- Education Item 2 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold rounded-full text-sm shadow-sm">
                        2015 - 2018
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">SMA Negeri 1 Jakarta</h3>
                    <p class="text-gray-700 mb-2 font-medium">Jurusan IPA</p>
                    <p class="text-gray-600">
                        Aktif dalam ekstrakurikuler programming dan olimpiade matematika.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Experience Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Work Experience</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Pengalaman kerja dan magang di berbagai perusahaan
            </p>
        </div>

        <div class="space-y-8">
            <!-- Experience Item 1 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-white to-blue-50 rounded-lg shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold rounded-full text-sm shadow-md">
                        <i class="fas fa-briefcase mr-2"></i>
                        2022 - Present
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">Senior Full Stack Developer</h3>
                    <p class="text-gray-700 mb-3 font-medium">PT. Tech Solutions Indonesia</p>
                    <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mengembangkan aplikasi web menggunakan PHP, Laravel, dan React</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Memimpin tim developer dalam project enterprise</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mengoptimalkan performa database dan aplikasi</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mentoring junior developer</li>
                    </ul>
                </div>
            </div>

            <!-- Experience Item 2 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-white to-green-50 rounded-lg shadow-lg border border-green-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-green-500 to-teal-500 text-white font-semibold rounded-full text-sm shadow-md">
                        <i class="fas fa-code mr-2"></i>
                        2021 - 2022
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">Junior Web Developer</h3>
                    <p class="text-gray-700 mb-3 font-medium">CV. Digital Creative</p>
                    <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mengembangkan website company profile dan e-commerce</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Maintenance dan update website client</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Kolaborasi dengan tim design untuk implementasi UI/UX</li>
                    </ul>
                </div>
            </div>

            <!-- Experience Item 3 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-white to-purple-50 rounded-lg shadow-lg border border-purple-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-full text-sm shadow-md">
                        <i class="fas fa-laptop mr-2"></i>
                        2020 - 2021
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">Freelance Web Developer</h3>
                    <p class="text-gray-700 mb-3 font-medium">Freelancer</p>
                    <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Mengerjakan project website untuk UMKM dan startup</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Membuat sistem informasi sederhana</li>
                        <li class="flex items-start"><i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>Konsultasi teknologi untuk client</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Organization Section -->
<section class="py-16 bg-gradient-to-br from-white to-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 animate-on-scroll">
            <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-4">Organization</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Organisasi dan komunitas yang pernah saya ikuti
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Organization Item 1 -->
            <div class="p-6 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg border border-blue-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">Himpunan Mahasiswa Teknik Informatika</h3>
                        <p class="text-gray-600 font-medium">2019 - 2021</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-2"><strong>Posisi:</strong> <span class="text-blue-600 font-semibold">Ketua Divisi IT</span></p>
                <p class="text-gray-600">
                    Bertanggung jawab mengembangkan website organisasi dan sistem informasi internal.
                </p>
            </div>

            <!-- Organization Item 2 -->
            <div class="p-6 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg border border-green-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-code text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors">Jakarta Developer Community</h3>
                        <p class="text-gray-600 font-medium">2020 - Present</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-2"><strong>Posisi:</strong> <span class="text-green-600 font-semibold">Core Member</span></p>
                <p class="text-gray-600">
                    Aktif dalam kegiatan sharing session dan workshop teknologi terbaru.
                </p>
            </div>

            <!-- Organization Item 3 -->
            <div class="p-6 bg-gradient-to-br from-purple-50 to-pink-100 rounded-lg border border-purple-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-graduation-cap text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">Google Developer Student Club</h3>
                        <p class="text-gray-600 font-medium">2020 - 2022</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-2"><strong>Posisi:</strong> <span class="text-purple-600 font-semibold">Lead</span></p>
                <p class="text-gray-600">
                    Memimpin komunitas mahasiswa dalam belajar teknologi Google dan mengadakan event.
                </p>
            </div>

            <!-- Organization Item 4 -->
            <div class="p-6 bg-gradient-to-br from-red-50 to-pink-100 rounded-lg border border-red-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-heart text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-red-600 transition-colors">Relawan IT untuk Indonesia</h3>
                        <p class="text-gray-600 font-medium">2021 - Present</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-2"><strong>Posisi:</strong> <span class="text-red-600 font-semibold">Volunteer</span></p>
                <p class="text-gray-600">
                    Membantu digitalisasi UMKM dan memberikan pelatihan teknologi gratis.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Activity Section -->
<section class="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 animate-on-scroll">
            <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4">Activities & Achievements</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Kegiatan dan pencapaian yang telah saya raih
            </p>
        </div>

        <div class="space-y-6">
            <!-- Activity Item 1 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-yellow-50 to-orange-100 rounded-lg shadow-lg border border-yellow-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-400 to-orange-500"></div>
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-bold rounded-full shadow-md group-hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        2023
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-yellow-600 transition-colors">
                        <div class="inline-flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mr-3 shadow-md group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-trophy text-white text-lg"></i>
                            </div>
                            Juara 1 Hackathon Nasional
                        </div>
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Memenangkan kompetisi hackathon dengan tema "Smart City Solution"
                        dengan mengembangkan aplikasi manajemen sampah berbasis IoT.
                    </p>
                    <div class="mt-3 inline-flex items-center px-3 py-1 bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-700 text-sm rounded-full border border-yellow-200">
                        <i class="fas fa-medal mr-2"></i>
                        First Place Winner
                    </div>
                </div>
            </div>

            <!-- Activity Item 2 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-lg border border-blue-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-indigo-500"></div>
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-bold rounded-full shadow-md group-hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        2022
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                        <div class="inline-flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center mr-3 shadow-md group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-microphone text-white text-lg"></i>
                            </div>
                            Speaker di TechTalk Indonesia
                        </div>
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Berbagi pengalaman tentang "Modern Web Development with PHP"
                        di hadapan 500+ developer se-Indonesia.
                    </p>
                    <div class="mt-3 inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm rounded-full border border-blue-200">
                        <i class="fas fa-users mr-2"></i>
                        500+ Attendees
                    </div>
                </div>
            </div>

            <!-- Activity Item 3 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg shadow-lg border border-green-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-emerald-500"></div>
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-bold rounded-full shadow-md group-hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        2021
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
                        <div class="inline-flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-3 shadow-md group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-certificate text-white text-lg"></i>
                            </div>
                            AWS Certified Solutions Architect
                        </div>
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Memperoleh sertifikasi AWS untuk kemampuan merancang dan
                        mengimplementasikan sistem terdistribusi di cloud.
                    </p>
                    <div class="mt-3 inline-flex items-center px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-sm rounded-full border border-green-200">
                        <i class="fas fa-cloud mr-2"></i>
                        Cloud Architecture
                    </div>
                </div>
            </div>

            <!-- Activity Item 4 -->
            <div class="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-br from-purple-50 to-pink-100 rounded-lg shadow-lg border border-purple-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-pink-500"></div>
                <div class="md:w-1/4">
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold rounded-full shadow-md group-hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        2020
                    </div>
                </div>
                <div class="md:w-3/4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">
                        <div class="inline-flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center mr-3 shadow-md group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-book text-white text-lg"></i>
                            </div>
                            Publikasi Paper Ilmiah
                        </div>
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Mempublikasikan paper tentang "Machine Learning untuk Prediksi Cuaca"
                        di jurnal internasional bereputasi.
                    </p>
                    <div class="mt-3 inline-flex items-center px-3 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-sm rounded-full border border-purple-200">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        International Journal
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
