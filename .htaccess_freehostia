# Portfolio Website - Optimized for Freehostia.com
# Security and Performance Settings

RewriteEngine On

# Force HTTPS (if SSL is available)
RewriteCond %{HTTPS} off
RewriteCond %{HTTP_HOST} !^localhost [NC]
RewriteCond %{HTTP_HOST} \.freehostia\.com$ [NC]
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security Headers (if mod_headers is available)
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Hide sensitive files and directories
<Files "config/database.php">
    Order allow,deny
    Deny from all
</Files>

<Files "config/database_freehostia.php">
    Order allow,deny
    <PERSON>y from all
</Files>

<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

<Files "*.md">
    Order allow,deny
    <PERSON>y from all
</Files>

<Files "*.log">
    Order allow,deny
    <PERSON>y from all
</Files>

# Prevent directory browsing
Options -Indexes

# Prevent access to backup and temporary files
<FilesMatch "\.(bak|backup|old|orig|original|tmp|temp|log)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Cache static files for better performance
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 30 days"
    ExpiresByType image/jpeg "access plus 30 days"
    ExpiresByType image/gif "access plus 30 days"
    ExpiresByType image/png "access plus 30 days"
    ExpiresByType image/webp "access plus 30 days"
    ExpiresByType image/svg+xml "access plus 30 days"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 7 days"
    ExpiresByType application/javascript "access plus 7 days"
    ExpiresByType application/x-javascript "access plus 7 days"
    ExpiresByType text/javascript "access plus 7 days"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML and other files
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
</IfModule>

# Compress files for faster loading (if mod_deflate is available)
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Protect against common attacks
<IfModule mod_rewrite.c>
    # Block suspicious query strings
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} ^.*(\[|\]|\(|\)|<|>|ê|"|;|\?|\*|=$).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*("|'|<|>|\|{||).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(%0|%A|%B|%C|%D|%E|%F|127\.0).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(request|select|insert|union|declare).* [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# Custom error pages
ErrorDocument 404 /index.php?page=home
ErrorDocument 403 /index.php?page=home
ErrorDocument 500 /index.php?page=home

# Prevent hotlinking (optional)
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?namaanda\.freehostia\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|css|js)$ - [F,L]
</IfModule>

# Set default charset
AddDefaultCharset UTF-8

# Disable server signature for security
ServerSignature Off
