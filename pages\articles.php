<?php
// Get articles from database
$articles = get_articles();
?>

<!-- Articles Header -->
<section class="bg-gradient-to-br from-primary via-indigo-600 to-secondary text-white py-16 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div class="absolute top-32 right-20 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
        <div class="absolute bottom-20 left-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
            <!-- Icon Badge -->
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-white/20 to-white/10 rounded-full shadow-lg mb-6 backdrop-blur-sm border border-white/30">
                <i class="fas fa-newspaper text-white text-3xl"></i>
            </div>

            <h1 class="text-4xl lg:text-5xl font-bold mb-4">
                <span class="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                    Articles & Blog
                </span>
            </h1>
            <p class="text-xl text-blue-100 max-w-2xl mx-auto mb-8">
                Berbagi pengetahuan dan pengalaman melalui artikel-artikel menarik tentang teknologi dan programming
            </p>

            <!-- Stats -->
            <div class="flex justify-center items-center gap-8 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"></div>
                    <span class="text-blue-200">Technology</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
                    <span class="text-blue-200">Programming</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full"></div>
                    <span class="text-blue-200">Tutorials</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Articles Content -->
<section class="py-16 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-40 h-40 bg-blue-200/20 rounded-full blur-3xl"></div>
        <div class="absolute bottom-32 right-20 w-56 h-56 bg-purple-200/15 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <?php if (empty($articles)): ?>
            <!-- No Articles State -->
            <div class="text-center py-16">
                <div class="max-w-md mx-auto bg-gradient-to-br from-white to-blue-50 rounded-2xl p-8 shadow-xl border border-blue-100">
                    <div class="w-20 h-20 bg-gradient-to-br from-gray-300 to-gray-400 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-newspaper text-white text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-semibold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent mb-4">
                        Belum Ada Artikel
                    </h3>
                    <p class="text-gray-600 mb-8">
                        Artikel-artikel menarik akan segera hadir. Stay tuned!
                    </p>
                    <a href="index.php?page=home"
                       class="bg-gradient-to-r from-primary to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i class="fas fa-home mr-2"></i>
                        Kembali ke Home
                    </a>
                </div>
            </div>
        <?php else: ?>
            <!-- Articles Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($articles as $article): ?>
                    <article class="group relative">
                        <div class="bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-4 border border-blue-200/50 relative">
                            <!-- Shimmer Effect -->
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                            <!-- Article Image -->
                            <div class="h-48 bg-gray-200 overflow-hidden relative">
                                <?php if (!empty($article['image'])): ?>
                                    <img src="assets/images/articles/<?php echo htmlspecialchars($article['image']); ?>"
                                         alt="<?php echo htmlspecialchars($article['title']); ?>"
                                         class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-primary via-indigo-600 to-secondary flex items-center justify-center">
                                        <i class="fas fa-newspaper text-4xl text-white group-hover:scale-125 group-hover:rotate-12 transition-all duration-500"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                                <!-- Floating Action Button -->
                                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                                    <div class="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors">
                                        <i class="fas fa-external-link-alt text-primary"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Article Content -->
                            <div class="p-6 relative z-10">
                                <!-- Top Gradient Border -->
                                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-purple-500 to-indigo-500"></div>

                                <!-- Category Badge -->
                                <div class="mb-4">
                                    <span class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-primary to-indigo-600 text-white text-xs font-semibold rounded-full shadow-lg">
                                        <i class="fas fa-tag mr-2"></i>
                                        <?php echo htmlspecialchars($article['category'] ?? 'General'); ?>
                                    </span>
                                </div>

                                <!-- Title -->
                                <h3 class="text-xl font-bold text-gray-900 mb-4 line-clamp-2 group-hover:text-primary transition-colors leading-tight">
                                    <?php echo htmlspecialchars($article['title']); ?>
                                </h3>

                                <!-- Excerpt -->
                                <p class="text-gray-600 mb-6 line-clamp-3 leading-relaxed">
                                    <?php echo truncate_text(strip_tags($article['content']), 120); ?>
                                </p>

                                <!-- Article Meta -->
                                <div class="flex items-center justify-between text-sm mb-6">
                                    <div class="flex items-center gap-2">
                                        <div class="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
                                            <i class="fas fa-calendar text-white text-xs"></i>
                                        </div>
                                        <span class="font-medium text-gray-700">
                                            <?php echo format_date($article['created_at']); ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center gap-2 bg-gradient-to-r from-gray-100 to-blue-100 px-3 py-2 rounded-full border border-gray-200">
                                        <i class="fas fa-eye text-gray-600"></i>
                                        <span class="font-medium text-gray-700">
                                            <?php echo number_format($article['views'] ?? 0); ?>
                                        </span>
                                    </div>
                                </div>

                                <!-- Read More Button -->
                                <a href="article.php?id=<?php echo $article['id']; ?>"
                                   class="group/btn inline-flex items-center bg-gradient-to-r from-primary to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                                    <span class="mr-2">Baca Selengkapnya</span>
                                    <i class="fas fa-arrow-right group-hover/btn:translate-x-1 transition-transform duration-300"></i>
                                </a>
                            </div>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination (if needed) -->
            <div class="mt-12 flex justify-center">
                <nav class="flex items-center space-x-2">
                    <a href="#" class="px-3 py-2 text-gray-500 hover:text-primary">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <a href="#" class="px-3 py-2 bg-primary text-white rounded">1</a>
                    <a href="#" class="px-3 py-2 text-gray-700 hover:text-primary">2</a>
                    <a href="#" class="px-3 py-2 text-gray-700 hover:text-primary">3</a>
                    <a href="#" class="px-3 py-2 text-gray-500 hover:text-primary">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Featured Articles Section -->
<section class="py-16 bg-gradient-to-br from-white via-gray-50 to-blue-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-40 h-40 bg-purple-200/20 rounded-full blur-3xl"></div>
        <div class="absolute bottom-32 right-20 w-56 h-56 bg-blue-200/15 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Enhanced Header -->
        <div class="text-center mb-16">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full shadow-lg mb-6">
                <i class="fas fa-fire text-white text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-primary to-purple-600 bg-clip-text text-transparent mb-4">
                Artikel Populer
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Artikel-artikel yang paling banyak dibaca dan disukai pembaca
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Popular Article 1 -->
            <div class="group relative">
                <div class="flex gap-6 p-6 bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-blue-200/50 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Icon Container -->
                    <div class="w-20 h-20 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 relative z-10">
                        <i class="fas fa-code text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 relative z-10">
                        <h3 class="text-lg font-bold text-gray-900 mb-3 group-hover:text-primary transition-colors">
                            Tips Optimasi Performance PHP
                        </h3>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                            Cara meningkatkan performa aplikasi PHP dengan berbagai teknik optimasi dan best practices...
                        </p>
                        <div class="flex items-center justify-between text-xs">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-calendar text-white text-xs"></i>
                                </div>
                                <span class="font-medium text-gray-700">15 Mei 2023</span>
                            </div>
                            <div class="flex items-center gap-2 bg-gradient-to-r from-blue-100 to-indigo-100 px-3 py-2 rounded-full border border-blue-200">
                                <i class="fas fa-eye text-blue-600"></i>
                                <span class="font-medium text-blue-700">1,234</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Popular Article 2 -->
            <div class="group relative">
                <div class="flex gap-6 p-6 bg-gradient-to-br from-white via-green-50 to-emerald-100 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-green-200/50 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Icon Container -->
                    <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 relative z-10">
                        <i class="fas fa-database text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 relative z-10">
                        <h3 class="text-lg font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
                            Database Design Best Practices
                        </h3>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                            Panduan lengkap merancang database yang efisien, scalable, dan maintainable untuk aplikasi modern...
                        </p>
                        <div class="flex items-center justify-between text-xs">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-calendar text-white text-xs"></i>
                                </div>
                                <span class="font-medium text-gray-700">10 Mei 2023</span>
                            </div>
                            <div class="flex items-center gap-2 bg-gradient-to-r from-green-100 to-emerald-100 px-3 py-2 rounded-full border border-green-200">
                                <i class="fas fa-eye text-green-600"></i>
                                <span class="font-medium text-green-700">987</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Popular Article 3 -->
            <div class="group relative">
                <div class="flex gap-6 p-6 bg-gradient-to-br from-white via-purple-50 to-pink-100 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-purple-200/50 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Icon Container -->
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 relative z-10">
                        <i class="fas fa-mobile-alt text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 relative z-10">
                        <h3 class="text-lg font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">
                            Responsive Design dengan Tailwind CSS
                        </h3>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                            Membuat website yang responsive dan modern menggunakan utility-first CSS framework...
                        </p>
                        <div class="flex items-center justify-between text-xs">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-calendar text-white text-xs"></i>
                                </div>
                                <span class="font-medium text-gray-700">5 Mei 2023</span>
                            </div>
                            <div class="flex items-center gap-2 bg-gradient-to-r from-purple-100 to-pink-100 px-3 py-2 rounded-full border border-purple-200">
                                <i class="fas fa-eye text-purple-600"></i>
                                <span class="font-medium text-purple-700">756</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Popular Article 4 -->
            <div class="group relative">
                <div class="flex gap-6 p-6 bg-gradient-to-br from-white via-yellow-50 to-orange-100 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-yellow-200/50 relative overflow-hidden">
                    <!-- Shimmer Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <!-- Icon Container -->
                    <div class="w-20 h-20 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 relative z-10">
                        <i class="fas fa-shield-alt text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 relative z-10">
                        <h3 class="text-lg font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors">
                            Web Security Fundamentals
                        </h3>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                            Dasar-dasar keamanan web yang harus diketahui setiap developer untuk aplikasi yang aman...
                        </p>
                        <div class="flex items-center justify-between text-xs">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-calendar text-white text-xs"></i>
                                </div>
                                <span class="font-medium text-gray-700">1 Mei 2023</span>
                            </div>
                            <div class="flex items-center gap-2 bg-gradient-to-r from-yellow-100 to-orange-100 px-3 py-2 rounded-full border border-yellow-200">
                                <i class="fas fa-eye text-orange-600"></i>
                                <span class="font-medium text-orange-700">654</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Subscription -->
<section class="py-16 bg-gradient-to-br from-primary via-indigo-600 to-secondary relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div class="absolute top-32 right-20 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
        <div class="absolute bottom-20 left-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <!-- Icon Badge -->
        <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-white/20 to-white/10 rounded-full shadow-lg mb-6 backdrop-blur-sm border border-white/30">
            <i class="fas fa-envelope text-white text-3xl"></i>
        </div>

        <h2 class="text-3xl font-bold text-white mb-4">
            <span class="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                Subscribe Newsletter
            </span>
        </h2>
        <p class="text-blue-100 mb-8 max-w-2xl mx-auto">
            Dapatkan artikel terbaru dan tips programming langsung di email Anda. Tidak ada spam, hanya konten berkualitas.
        </p>

        <!-- Newsletter Form -->
        <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto mb-8">
            <input type="email"
                   placeholder="Masukkan email Anda"
                   class="flex-1 px-6 py-4 rounded-xl border-0 focus:ring-2 focus:ring-white focus:outline-none bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500">
            <button type="submit"
                    class="bg-gradient-to-r from-white to-blue-50 text-primary px-8 py-4 rounded-xl font-semibold hover:from-blue-50 hover:to-white transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                <i class="fas fa-paper-plane mr-2"></i>
                Subscribe
            </button>
        </form>

        <!-- Social Links -->
        <div class="flex justify-center gap-4">
            <a href="#" class="w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 rounded-full flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-lg backdrop-blur-sm border border-white/30">
                <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 rounded-full flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-lg backdrop-blur-sm border border-white/30">
                <i class="fab fa-linkedin"></i>
            </a>
            <a href="#" class="w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 rounded-full flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-lg backdrop-blur-sm border border-white/30">
                <i class="fab fa-github"></i>
            </a>
        </div>
    </div>
</section>
