<?php
// Get articles from database
$articles = get_articles();
?>

<!-- Articles Header -->
<section class="bg-gradient-to-r from-primary to-secondary text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl lg:text-5xl font-bold mb-4">Articles</h1>
            <p class="text-xl text-blue-100 max-w-2xl mx-auto">
                Be<PERSON><PERSON> pengetahuan dan pengalaman melalui artikel-artikel menarik
            </p>
        </div>
    </div>
</section>

<!-- Articles Content -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php if (empty($articles)): ?>
            <!-- No Articles State -->
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <i class="fas fa-newspaper text-6xl text-gray-300 mb-6"></i>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Belum Ada Artikel</h3>
                    <p class="text-gray-600 mb-8">
                        Artikel-artikel menarik akan segera hadir. Stay tuned!
                    </p>
                    <a href="index.php?page=home" 
                       class="bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-secondary transition-colors">
                        Kembali ke Home
                    </a>
                </div>
            </div>
        <?php else: ?>
            <!-- Articles Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($articles as $article): ?>
                    <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                        <!-- Article Image -->
                        <div class="h-48 bg-gray-200 overflow-hidden">
                            <?php if (!empty($article['image'])): ?>
                                <img src="assets/images/articles/<?php echo htmlspecialchars($article['image']); ?>" 
                                     alt="<?php echo htmlspecialchars($article['title']); ?>" 
                                     class="w-full h-full object-cover">
                            <?php else: ?>
                                <div class="w-full h-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
                                    <i class="fas fa-newspaper text-4xl text-white"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Article Content -->
                        <div class="p-6">
                            <!-- Category & Date -->
                            <div class="flex items-center justify-between mb-3">
                                <span class="px-3 py-1 bg-primary text-white text-xs rounded-full">
                                    <?php echo htmlspecialchars($article['category'] ?? 'General'); ?>
                                </span>
                                <span class="text-gray-500 text-sm">
                                    <?php echo format_date($article['created_at']); ?>
                                </span>
                            </div>
                            
                            <!-- Title -->
                            <h3 class="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                                <?php echo htmlspecialchars($article['title']); ?>
                            </h3>
                            
                            <!-- Excerpt -->
                            <p class="text-gray-600 mb-4 line-clamp-3">
                                <?php echo truncate_text(strip_tags($article['content']), 120); ?>
                            </p>
                            
                            <!-- Read More -->
                            <div class="flex items-center justify-between">
                                <a href="article.php?id=<?php echo $article['id']; ?>" 
                                   class="text-primary hover:text-secondary font-semibold text-sm">
                                    Baca Selengkapnya →
                                </a>
                                <div class="flex items-center text-gray-500 text-sm">
                                    <i class="fas fa-eye mr-1"></i>
                                    <span><?php echo number_format($article['views'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination (if needed) -->
            <div class="mt-12 flex justify-center">
                <nav class="flex items-center space-x-2">
                    <a href="#" class="px-3 py-2 text-gray-500 hover:text-primary">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <a href="#" class="px-3 py-2 bg-primary text-white rounded">1</a>
                    <a href="#" class="px-3 py-2 text-gray-700 hover:text-primary">2</a>
                    <a href="#" class="px-3 py-2 text-gray-700 hover:text-primary">3</a>
                    <a href="#" class="px-3 py-2 text-gray-500 hover:text-primary">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Featured Articles Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Artikel Populer</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Artikel-artikel yang paling banyak dibaca
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Popular Article 1 -->
            <div class="flex gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="w-20 h-20 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-code text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-2">Tips Optimasi Performance PHP</h3>
                    <p class="text-gray-600 text-sm mb-2">
                        Cara meningkatkan performa aplikasi PHP dengan berbagai teknik optimasi...
                    </p>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>15 Mei 2023</span>
                        <span><i class="fas fa-eye mr-1"></i>1,234 views</span>
                    </div>
                </div>
            </div>
            
            <!-- Popular Article 2 -->
            <div class="flex gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-database text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-2">Database Design Best Practices</h3>
                    <p class="text-gray-600 text-sm mb-2">
                        Panduan lengkap merancang database yang efisien dan scalable...
                    </p>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>10 Mei 2023</span>
                        <span><i class="fas fa-eye mr-1"></i>987 views</span>
                    </div>
                </div>
            </div>
            
            <!-- Popular Article 3 -->
            <div class="flex gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-mobile-alt text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-2">Responsive Design dengan Tailwind CSS</h3>
                    <p class="text-gray-600 text-sm mb-2">
                        Membuat website yang responsive menggunakan utility-first CSS framework...
                    </p>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>5 Mei 2023</span>
                        <span><i class="fas fa-eye mr-1"></i>756 views</span>
                    </div>
                </div>
            </div>
            
            <!-- Popular Article 4 -->
            <div class="flex gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="w-20 h-20 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-shield-alt text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-2">Web Security Fundamentals</h3>
                    <p class="text-gray-600 text-sm mb-2">
                        Dasar-dasar keamanan web yang harus diketahui setiap developer...
                    </p>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>1 Mei 2023</span>
                        <span><i class="fas fa-eye mr-1"></i>654 views</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Subscription -->
<section class="py-16 bg-primary">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-white mb-4">Subscribe Newsletter</h2>
        <p class="text-blue-100 mb-8 max-w-2xl mx-auto">
            Dapatkan artikel terbaru langsung di email Anda. Tidak ada spam, hanya konten berkualitas.
        </p>
        <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input type="email" 
                   placeholder="Masukkan email Anda" 
                   class="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:outline-none">
            <button type="submit" 
                    class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Subscribe
            </button>
        </form>
    </div>
</section>
